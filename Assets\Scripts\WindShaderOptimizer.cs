using UnityEngine;

/// <summary>
/// Optimizes wind shader performance by pre-calculating expensive operations on CPU
/// and passing them to the shader as uniform values.
/// </summary>
[RequireComponent(typeof(SpriteRenderer))]
public class WindShaderOptimizer : MonoBehaviour
{
    [Header("Performance Settings")]
    [SerializeField] private bool enableOptimization = true;
    [SerializeField] private float updateFrequency = 60f; // Updates per second
    
    private SpriteRenderer spriteRenderer;
    private Material materialInstance;
    private float lastUpdateTime;
    private float updateInterval;
    
    // Shader property IDs for performance
    private static readonly int WindTimeDataID = Shader.PropertyToID("_WindTimeData");
    private static readonly int WindDirectionNormID = Shader.PropertyToID("_WindDirectionNorm");
    private static readonly int WindSpeedID = Shader.PropertyToID("_WindSpeed");
    private static readonly int WindDirectionID = Shader.PropertyToID("_WindDirection");
    private static readonly int WindPulseFrequencyID = Shader.PropertyToID("_WindPulseFrequency");
    private static readonly int WindPulseAmplitudeID = Shader.PropertyToID("_WindPulseAmplitude");

    private void Awake()
    {
        spriteRenderer = GetComponent<SpriteRenderer>();
        updateInterval = 1f / updateFrequency;
        
        // Create material instance to avoid affecting other sprites
        if (spriteRenderer.material != null)
        {
            materialInstance = new Material(spriteRenderer.material);
            spriteRenderer.material = materialInstance;
        }
    }

    private void Start()
    {
        if (enableOptimization && materialInstance != null)
        {
            UpdateShaderProperties();
        }
    }

    private void Update()
    {
        if (!enableOptimization || materialInstance == null) return;
        
        // Update at specified frequency to balance performance and smoothness
        if (Time.time - lastUpdateTime >= updateInterval)
        {
            UpdateShaderProperties();
            lastUpdateTime = Time.time;
        }
    }

    private void UpdateShaderProperties()
    {
        // Get current shader properties
        float windSpeed = materialInstance.GetFloat(WindSpeedID);
        Vector4 windDirection = materialInstance.GetVector(WindDirectionID);
        float windPulseFreq = materialInstance.GetFloat(WindPulseFrequencyID);
        float windPulseAmp = materialInstance.GetFloat(WindPulseAmplitudeID);
        
        // Pre-calculate time-based values
        float currentTime = Time.time * windSpeed;
        float pulseValue = Mathf.Sin(currentTime * windPulseFreq);
        
        // Pre-normalize wind direction
        Vector2 windDir2D = new Vector2(windDirection.x, windDirection.y);
        Vector2 normalizedWindDir = windDir2D.normalized;
        
        // Pack pre-calculated values into Vector4 for efficiency
        Vector4 windTimeData = new Vector4(
            currentTime,           // x: time * speed
            pulseValue,           // y: sin(pulse)
            Mathf.Cos(currentTime * windPulseFreq), // z: cos(pulse) - for future use
            windPulseAmp          // w: pulse amplitude
        );
        
        // Update shader properties
        materialInstance.SetVector(WindTimeDataID, windTimeData);
        materialInstance.SetVector(WindDirectionNormID, normalizedWindDir);
    }

    private void OnDestroy()
    {
        // Clean up material instance
        if (materialInstance != null)
        {
            DestroyImmediate(materialInstance);
        }
    }

    private void OnValidate()
    {
        // Clamp update frequency to reasonable values
        updateFrequency = Mathf.Clamp(updateFrequency, 10f, 120f);
        updateInterval = 1f / updateFrequency;
    }

    /// <summary>
    /// Manually trigger shader property update (useful for testing)
    /// </summary>
    [ContextMenu("Force Update Shader Properties")]
    public void ForceUpdateShaderProperties()
    {
        if (materialInstance != null)
        {
            UpdateShaderProperties();
        }
    }

    /// <summary>
    /// Toggle optimization on/off at runtime
    /// </summary>
    public void SetOptimizationEnabled(bool enabled)
    {
        enableOptimization = enabled;
        if (enabled && materialInstance != null)
        {
            UpdateShaderProperties();
        }
    }
}
