fileFormatVersion: 2
guid: e3765d613d1a9f64c8cec6b9646ffb7a
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 0
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: tall dry grass ish_0
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 32
        height: 32
      alignment: 9
      pivot: {x: 0.5, y: 0.25}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 40caf1acf54ce50408e68a9b19c7ee9e
      internalID: -847095374
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tall dry grass ish_1
      rect:
        serializedVersion: 2
        x: 32
        y: 0
        width: 32
        height: 32
      alignment: 9
      pivot: {x: 0.5, y: 0.25}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8e0f92f1a414f114bb588dbde8f094ee
      internalID: 851239587
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tall dry grass ish_12
      rect:
        serializedVersion: 2
        x: 128
        y: 0
        width: 32
        height: 32
      alignment: 9
      pivot: {x: 0.5, y: 0.15625}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d1c5df486faa2ee469e88f64458117a2
      internalID: 1934849285
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tall dry grass ish_13
      rect:
        serializedVersion: 2
        x: 160
        y: 0
        width: 32
        height: 32
      alignment: 9
      pivot: {x: 0.53125, y: 0.21875}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0f346b86b550c6d41aef79ac7989a26e
      internalID: -1538322079
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tall dry grass ish_18
      rect:
        serializedVersion: 2
        x: 192
        y: 0
        width: 32
        height: 32
      alignment: 9
      pivot: {x: 0.53125, y: 0.125}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 35746f1972e14b241b798a3238b6d32a
      internalID: 1342000238
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tall dry grass ish_19
      rect:
        serializedVersion: 2
        x: 224
        y: 0
        width: 32
        height: 32
      alignment: 9
      pivot: {x: 0.53125, y: 0.125}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cdd400793b7f6c540aee2a1656756fda
      internalID: -531265678
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tall dry grass ish_6
      rect:
        serializedVersion: 2
        x: 64
        y: 0
        width: 32
        height: 32
      alignment: 9
      pivot: {x: 0.5, y: 0.09375}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 51173aef3dea5d54c8b340f2a91522b3
      internalID: 1801242692
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tall dry grass ish_7
      rect:
        serializedVersion: 2
        x: 96
        y: 0
        width: 32
        height: 32
      alignment: 9
      pivot: {x: 0.5, y: 0.15625}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 123cb3ffdc6966147a1410c72a906da7
      internalID: -2014020807
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: a60db371670ffac4d828b6e3a5bf12a3
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      tall dry grass ish_0: -847095374
      tall dry grass ish_1: 851239587
      tall dry grass ish_12: 1934849285
      tall dry grass ish_13: -1538322079
      tall dry grass ish_18: 1342000238
      tall dry grass ish_19: -531265678
      tall dry grass ish_6: 1801242692
      tall dry grass ish_7: -2014020807
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
