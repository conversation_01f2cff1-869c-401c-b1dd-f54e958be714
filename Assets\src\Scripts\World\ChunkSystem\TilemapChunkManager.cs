using UnityEngine;
using UnityEngine.Tilemaps;
using System.Collections.Generic;
using System.Text;
using Sirenix.OdinInspector;
using SplatterSystem;
[RequireComponent(typeof(Grid))]
public class TilemapChunkManager : MonoBehaviour
{
    // Singleton
    public static TilemapChunkManager Instance { get; private set; }
    
    // References
    private VegetationSpawnManager vegetationSpawnManager;

    [Title("Chunk Settings")]
    [FoldoutGroup("Configuration")]
    [SerializeField, Range(16, 64)]
    [Tooltip("Width of each chunk in tiles")]
    private int chunkWidth = 32;
    
    [FoldoutGroup("Configuration")]
    [SerializeField, Range(16, 64)]
    [Tooltip("Height of each chunk in tiles")]
    private int chunkHeight = 32;

    [FoldoutGroup("Configuration")]
    [SerializeField, Range(1, 5)]
    [Tooltip("View distance in chunks (1 = 3x3 grid, 2 = 5x5 grid, 3 = 7x7 grid)")]
    [InfoBox("Total chunks generated = (2 * viewDistance + 1)²")]
    private int viewDistance = 2;
    
    [FoldoutGroup("Configuration")]
    [SerializeField, Range(0, 3)]
    [Tooltip("Additional chunks to keep loaded beyond view distance for smoother transitions")]
    [InfoBox("Total chunks kept loaded = (2 * (viewDistance + unloadBuffer) + 1)²")]
    private int unloadBufferDistance = 1;

    [FoldoutGroup("Configuration")]
    [SerializeField]
    [Tooltip("Transform of the player to track")]
    private Transform playerTransform;

    [Title("Tilemap References")]
    [FoldoutGroup("Tilemaps")]
    [SerializeField, Required]
    [Tooltip("Tilemap for ground tiles")]
    private Tilemap groundTilemap;

    [FoldoutGroup("Tilemaps")]
    [SerializeField, Required]
    [Tooltip("Tilemap for decoration tiles")]
    private Tilemap decorationTilemap;

    [FoldoutGroup("Tilemaps")]
    [SerializeField]
    [Tooltip("Tilemap for collision tiles")]
    private Tilemap collisionTilemap;

    [Title("Biome System")]
    [FoldoutGroup("Biomes")]
    [SerializeField]
    private BiomeManager biomeManager = new BiomeManager();

    [Title("Generation")]
    [FoldoutGroup("Generation")]
    [SerializeField]
    [Tooltip("World generation seed")]
    private int worldSeed = 42;

    [Title("Debug")]
    [FoldoutGroup("Debug")]
    [SerializeField]
    private bool showChunkBorders = false;

    [FoldoutGroup("Debug")]
    [SerializeField]
    private GameObject chunkBorderPrefab;

    [Title("Runtime Info")]
    [ShowInInspector, ReadOnly]
    [InfoBox("Chunks are generated at view distance, but only unloaded at view distance + buffer distance")]
    private Dictionary<ChunkCoordinate, ChunkData> generatedChunks = new Dictionary<ChunkCoordinate, ChunkData>(100); // Pre-sized to avoid resizing

    [ShowInInspector, ReadOnly]
    private ChunkCoordinate currentPlayerChunk;

    [ShowInInspector, ReadOnly]
    private int totalChunksGenerated = 0;
    
    [ShowInInspector, ReadOnly]
    [InfoBox("Shows how many chunks are currently loaded in memory")]
    private int ActiveChunkCount => generatedChunks?.Count ?? 0;
    
    [ShowInInspector, ReadOnly]
    private string ChunkGridInfo => $"Generation: {viewDistance * 2 + 1}x{viewDistance * 2 + 1} = {(viewDistance * 2 + 1) * (viewDistance * 2 + 1)} chunks | " +
                                    $"Loaded: {(viewDistance + unloadBufferDistance) * 2 + 1}x{(viewDistance + unloadBufferDistance) * 2 + 1} = {((viewDistance + unloadBufferDistance) * 2 + 1) * ((viewDistance + unloadBufferDistance) * 2 + 1)} chunks";

    [ShowInInspector, ReadOnly]
    private Bounds activeBounds;
    
    [BoxGroup("Stain System")]
    [SerializeField]
    private GameObject splatterAreaPrefab;
    
    [BoxGroup("Stain System")]
    [SerializeField]
    private bool clearSplatterOnDeactivate = false;
    
    [BoxGroup("Stain System")]
    [SerializeField]
    private bool debugSplatterAreas = false;
    private Dictionary<ChunkCoordinate, GameObject> chunkBorders = new Dictionary<ChunkCoordinate, GameObject>(50); // Pre-sized to avoid resizing
    // Tracks one SplatterArea GameObject per chunk. Objects werden nur einmal erstellt und anschließend ein- bzw. ausgeblendet.
    private readonly Dictionary<ChunkCoordinate, GameObject> chunkSplatterAreas = new Dictionary<ChunkCoordinate, GameObject>(50);
    private bool isInitialized = false;
    private Grid grid;

    // Cached collection to avoid allocations each time we query the currently visible chunks.
    // The content is rebuilt whenever UpdateVisibleChunks is executed.
    private readonly HashSet<ChunkCoordinate> visibleChunksCache = new HashSet<ChunkCoordinate>();

    // Re-usable list for temporary bookkeeping inside UpdateChunkBorders to prevent per-frame allocations.
    private readonly List<ChunkCoordinate> chunkBordersToRemove = new List<ChunkCoordinate>();
    
    // Re-usable list for bookkeeping inside ClearDistantTiles to prevent per-frame allocations.
    private readonly List<ChunkCoordinate> chunksToClearCache = new List<ChunkCoordinate>();

    // Cache for an empty block of tiles used for clearing chunks efficiently.
    private TileBase[] emptyChunkTileArray;
    
    // Cached arrays for tile generation to prevent GC allocations
    private TileBase[] groundTilesCache;
    private readonly List<Vector3Int> decorPositionsCache = new List<Vector3Int>();
    private readonly List<TileBase> decorTilesCache = new List<TileBase>();
    
    // Multiple pre-allocated arrays for different decoration counts
    private Vector3Int[] decorPositions16;
    private TileBase[] decorTiles16;
    private Vector3Int[] decorPositions64;
    private TileBase[] decorTiles64;
    private Vector3Int[] decorPositions256;
    private TileBase[] decorTiles256;
    
    // Cached structs to avoid allocations in hot paths
    private Vector3Int cachedChunkWorldPos;
    private BoundsInt cachedChunkBounds;
    private Vector3Int cachedChunkBoundsMax;  // For BoundsInt.SetMinMax
    private Vector3Int cachedTilePos;
    private Vector3Int cachedDecorOffset;
    private Vector3Int cachedChunkCenterPos;
    private ChunkCoordinate cachedPlayerChunk;
    private ChunkCoordinate[] visibleChunkOffsets;
    private Vector3 cachedMinBounds = new Vector3();
    private Vector3 cachedMaxBounds = new Vector3();
    
    // Cached for chunk clearing
    private BoundsInt cachedClearChunkBounds;
    private Vector3Int cachedClearChunkPos;
    private Vector3Int cachedClearChunkSize;
    
    // StringBuilder for debug operations
    private StringBuilder chunkBorderNameBuilder = new StringBuilder();
    private StringBuilder logInfoBuilder = new StringBuilder();
    
    // Cache for RegenerateWorld
    private List<ChunkCoordinate> regenerateWorldChunksCache = new List<ChunkCoordinate>();
    
    // Cache for CreateChunkBorder
    private Material chunkBorderMaterial;
    private Vector3[] chunkBorderPositions = new Vector3[5];
    
    // Pre-calculated chunk coordinate offsets for RecalculateVisibleChunks
    private int visibleChunkOffsetsCount;

    void Awake()
    {
        // Set singleton instance
        if (Instance == null)
            Instance = this;
        else if (Instance != this)
            Destroy(gameObject);

        grid = GetComponent<Grid>();
        int tileCount = chunkWidth * chunkHeight;
        emptyChunkTileArray = new TileBase[tileCount];
        groundTilesCache = new TileBase[tileCount];
        
        // Pre-allocate decoration arrays of common sizes
        decorPositions16 = new Vector3Int[16];
        decorTiles16 = new TileBase[16];
        decorPositions64 = new Vector3Int[64];
        decorTiles64 = new TileBase[64];
        decorPositions256 = new Vector3Int[256];
        decorTiles256 = new TileBase[256];
        
        // Pre-calculate chunk coordinate offsets for view distance
        PrecalculateVisibleChunkOffsets();
        
        SetupTilemaps();
    }

    void Start()
    {
        // Get or create VegetationSpawnManager BEFORE initialization
        vegetationSpawnManager = GetComponent<VegetationSpawnManager>();
        if (vegetationSpawnManager == null)
        {
            vegetationSpawnManager = gameObject.AddComponent<VegetationSpawnManager>();
        }
        
        Initialize();
    }
    
    void PrecalculateVisibleChunkOffsets()
    {
        // Calculate total number of visible chunks
        int diameter = viewDistance * 2 + 1;
        visibleChunkOffsetsCount = diameter * diameter;
        visibleChunkOffsets = new ChunkCoordinate[visibleChunkOffsetsCount];
        
        int index = 0;
        for (int dx = -viewDistance; dx <= viewDistance; dx++)
        {
        for (int dy = -viewDistance; dy <= viewDistance; dy++)
        {
            visibleChunkOffsets[index++] = new ChunkCoordinate(dx, dy);
        }
        }
    }

    void SetupTilemaps()
    {
        if (groundTilemap == null)
        {
        GameObject groundObj = new GameObject("Ground Tilemap");
        groundObj.transform.SetParent(transform);
        groundObj.transform.localPosition = Vector3.zero;
        groundTilemap = groundObj.AddComponent<Tilemap>();
        TilemapRenderer groundRenderer = groundObj.AddComponent<TilemapRenderer>();
        groundRenderer.sortingLayerName = "Default";
        groundRenderer.sortingOrder = -1000; // Always render below everything else
        // Ground doesn't need Individual mode since it's always below
        }

        if (decorationTilemap == null)
        {
        GameObject decorObj = new GameObject("Decoration Tilemap");
        decorObj.transform.SetParent(transform);
        decorObj.transform.localPosition = Vector3.zero;
        decorationTilemap = decorObj.AddComponent<Tilemap>();
        TilemapRenderer decorRenderer = decorObj.AddComponent<TilemapRenderer>();
        decorRenderer.sortingLayerName = "Default";
        decorRenderer.sortingOrder = 0; // Same as vegetation for Y-sorting
        decorRenderer.mode = TilemapRenderer.Mode.Individual; // Enable Y-sorting for decorations too
        }

        if (collisionTilemap == null)
        {
        GameObject collisionObj = new GameObject("Collision Tilemap");
        collisionObj.transform.SetParent(transform);
        collisionObj.transform.localPosition = Vector3.zero;
        collisionTilemap = collisionObj.AddComponent<Tilemap>();
        collisionObj.AddComponent<TilemapCollider2D>();
        TilemapRenderer collisionRenderer = collisionObj.AddComponent<TilemapRenderer>();
        collisionRenderer.enabled = false;
        }
    }

    void Initialize()
    {
        if (isInitialized) return;

        if (playerTransform == null)
        {
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            playerTransform = player.transform;
        }
        else
        {
            Debug.LogError("No player transform assigned and no GameObject with 'Player' tag found!");
            return;
        }
        }

        biomeManager.Initialize(worldSeed);

        ChunkCoordinate startChunk = ChunkCoordinate.FromWorldPosition(playerTransform.position, chunkWidth, chunkHeight);
        currentPlayerChunk = startChunk;
        cachedPlayerChunk = startChunk;  // Initialize cached chunk as well
        
        GenerateChunk(startChunk);
        UpdateVisibleChunks();

        isInitialized = true;
    }

    void Update()
    {
        if (!isInitialized || playerTransform == null) return;

        // Use cached struct to avoid allocation
        int chunkX = Mathf.FloorToInt(playerTransform.position.x / chunkWidth);
        int chunkY = Mathf.FloorToInt(playerTransform.position.y / chunkHeight);
        cachedPlayerChunk.x = chunkX;
        cachedPlayerChunk.y = chunkY;
        
        if (cachedPlayerChunk != currentPlayerChunk)
        {
        currentPlayerChunk = cachedPlayerChunk;
        UpdateVisibleChunks();
        }
    }

    void UpdateVisibleChunks()
    {
        // Rebuild the cache only once per visibility update.
        RecalculateVisibleChunks();

        foreach (var coord in visibleChunksCache)
        {
        if (!generatedChunks.ContainsKey(coord))
        {
            GenerateChunk(coord);
        }
        }

        UpdateActiveBounds();
        ClearDistantTiles();
        
        // Update chunk borders if enabled
        if (showChunkBorders)
        {
        UpdateChunkBorders(visibleChunksCache);
        }

        // Manage splatter areas for visible chunks
        UpdateChunkSplatterAreas(visibleChunksCache);
    }

    /// <summary>
    /// Rebuilds <see cref="visibleChunksCache"/> based on the current player chunk and <see cref="viewDistance"/>.
    /// This method is allocation-free after the first invocation.
    /// </summary>
    void RecalculateVisibleChunks()
    {
        visibleChunksCache.Clear();

        // Use pre-calculated offsets to avoid allocations
        for (int i = 0; i < visibleChunkOffsetsCount; i++)
        {
        ChunkCoordinate offset = visibleChunkOffsets[i];
        // Manual addition to avoid operator allocation
        ChunkCoordinate coord = new ChunkCoordinate(
            currentPlayerChunk.x + offset.x,
            currentPlayerChunk.y + offset.y
        );
        visibleChunksCache.Add(coord);
        }
    }

    // Lightweight accessor primarily used by editor/debug code where GC allocations are less critical.
    // Returns a reference to the cached set after ensuring it is up-to-date.
    HashSet<ChunkCoordinate> GetVisibleChunks()
    {
        RecalculateVisibleChunks();
        return visibleChunksCache;
    }

    void GenerateChunk(ChunkCoordinate coord)
    {
        if (generatedChunks.ContainsKey(coord)) return;

        int chunkSeed = worldSeed + coord.GetHashCode();
        
        // Get the biome at the chunk center for legacy ChunkData (chunks can now have multiple biomes)
        cachedChunkCenterPos.x = coord.x * chunkWidth + chunkWidth / 2;
        cachedChunkCenterPos.y = coord.y * chunkHeight + chunkHeight / 2;
        cachedChunkCenterPos.z = 0;
        BiomeData dominantBiome = biomeManager.GetBiomeForWorldPosition(cachedChunkCenterPos, GetChunkSize());
        
        ChunkData chunkData = new ChunkData(coord, dominantBiome, chunkSeed);
        
        GenerateTilesForChunk(coord, chunkSeed);
        
        chunkData.isGenerated = true;
        generatedChunks[coord] = chunkData;
        totalChunksGenerated++;

        if (showChunkBorders)
        {
        CreateChunkBorder(coord);
        }
        
        // Notify vegetation spawn manager
        if (vegetationSpawnManager != null)
        {
            vegetationSpawnManager.OnChunkGenerated(coord, chunkSeed);
        }

        // Logging removed for performance
    }

    void GenerateTilesForChunk(ChunkCoordinate coord, int seed)
    {
        // Use cached structs instead of allocating new ones
        cachedChunkWorldPos.x = coord.x * chunkWidth;
        cachedChunkWorldPos.y = coord.y * chunkHeight;
        cachedChunkWorldPos.z = 0;
        
        // Use cached Vector3Int for max bounds to avoid allocation
        cachedChunkBoundsMax.x = cachedChunkWorldPos.x + chunkWidth;
        cachedChunkBoundsMax.y = cachedChunkWorldPos.y + chunkHeight;
        cachedChunkBoundsMax.z = cachedChunkWorldPos.z + 1;
        cachedChunkBounds.SetMinMax(cachedChunkWorldPos, cachedChunkBoundsMax);
        
        // Ensure cache arrays are correct size (handles runtime chunk size changes)
        int requiredSize = chunkWidth * chunkHeight;
        if (groundTilesCache == null || groundTilesCache.Length != requiredSize)
        {
        groundTilesCache = new TileBase[requiredSize];
        }

        Random.State originalState = Random.state;
        Random.InitState(seed);

        // First pass: Generate ground tiles for the entire chunk at once
        for (int y = 0; y < chunkHeight; y++)
        {
        for (int x = 0; x < chunkWidth; x++)
        {
            // Reuse cached Vector3Int to avoid allocation
            cachedTilePos.x = cachedChunkWorldPos.x + x;
            cachedTilePos.y = cachedChunkWorldPos.y + y;
            cachedTilePos.z = 0;
            BiomeData tileBiome = biomeManager.GetBiomeForWorldPosition(cachedTilePos, GetChunkSize());
            
            float noiseValue = TileGenerator.GenerateHeight(cachedTilePos.x, cachedTilePos.y, tileBiome, seed);
            TileBase tileToPlace;
        
            if (tileBiome.SecondaryGroundTiles != null && tileBiome.SecondaryGroundTiles.Length > 0)
            {
            if (tileBiome.UseNoiseBasedTileSelection)
            {
                tileToPlace = GetTileFromNoiseDistribution(noiseValue, tileBiome);
            }
            else
            {
                if (Random.value < tileBiome.SecondaryTileChance)
                {
                tileToPlace = tileBiome.GetRandomSecondaryTile();
                }
                else
                {
                tileToPlace = tileBiome.PrimaryGroundTile;
                }
            }
            }
            else
            {
            tileToPlace = tileBiome.PrimaryGroundTile;
            }

            int index = y * chunkWidth + x;
            groundTilesCache[index] = tileToPlace;
        }
        }
        
        groundTilemap.SetTilesBlock(cachedChunkBounds, groundTilesCache);
        
        // Restore state before next random operation
        Random.state = originalState;

        // Second pass: Generate transition tiles (cannot be easily batched due to neighbor dependency)
        Random.InitState(seed + 500);
        for (int x = 0; x < chunkWidth; x++)
        {
        for (int y = 0; y < chunkHeight; y++)
        {
            // Reuse cached Vector3Int to avoid allocation
            cachedTilePos.x = cachedChunkWorldPos.x + x;
            cachedTilePos.y = cachedChunkWorldPos.y + y;
            cachedTilePos.z = 0;
            GenerateTransitionTileAt(cachedTilePos, null, seed + 500);
        }
        }

        // Third pass: Generate decorations (batched)
        GenerateDecorationsForMixedChunk(coord, cachedChunkWorldPos, seed);
    }

    private TileBase GetTileFromNoiseDistribution(float noiseValue, BiomeData biome)
    {
        float normalizedNoise = (noiseValue + 1f) * 0.5f;
        normalizedNoise = Mathf.Clamp01(normalizedNoise);
        
        float totalTileTypes = biome.SecondaryGroundTiles.Length + 1;
        float primaryThreshold = (1f - biome.SecondaryTileChance) / totalTileTypes;
        float secondaryZoneSize = biome.SecondaryTileChance / biome.SecondaryGroundTiles.Length;
        
        if (normalizedNoise < primaryThreshold)
        {
        return biome.PrimaryGroundTile;
        }
        
        int secondaryIndex = Mathf.FloorToInt((normalizedNoise - primaryThreshold) / secondaryZoneSize);
        secondaryIndex = Mathf.Clamp(secondaryIndex, 0, biome.SecondaryGroundTiles.Length - 1);
        
        return biome.SecondaryGroundTiles[secondaryIndex];
    }

    void GenerateTransitionTileAt(Vector3Int worldPos, BiomeData currentBiome, int seed)
    {
        // Delegate to the TransitionTileGenerator for modular handling
        // currentBiome parameter is no longer used, as TransitionTileGenerator will determine biomes per tile
        TransitionTileGenerator.GenerateTransitionTileAt(
        worldPos,
        decorationTilemap,
        biomeManager,
        GetChunkSize(),
        false // Logging disabled for performance
        );
    }

    void GenerateDecorationsForMixedChunk(ChunkCoordinate coord, Vector3Int chunkWorldPos, int seed)
    {
        Random.InitState(seed + 1000);

        // Direct array filling instead of using lists
        int decorCount = 0;
        Vector3Int[] positions = null;
        TileBase[] tiles = null;
        
        // Choose appropriate pre-allocated array based on chunk size
        int maxPossibleDecor = (chunkWidth / 2) * (chunkHeight / 2);
        if (maxPossibleDecor <= 16)
        {
        positions = decorPositions16;
        tiles = decorTiles16;
        }
        else if (maxPossibleDecor <= 64)
        {
        positions = decorPositions64;
        tiles = decorTiles64;
        }
        else
        {
        positions = decorPositions256;
        tiles = decorTiles256;
        }

        for (int x = 0; x < chunkWidth; x += 2)
        {
        for (int y = 0; y < chunkHeight; y += 2)
        {
            if (decorCount >= positions.Length) break; // Safety check
            
            // Reuse cached Vector3Int to avoid allocation
            cachedDecorOffset.x = chunkWorldPos.x + x + Random.Range(0, 2);
            cachedDecorOffset.y = chunkWorldPos.y + y + Random.Range(0, 2);
            cachedDecorOffset.z = 0;
            
            // Get biome for this decoration position
            BiomeData biome = biomeManager.GetBiomeForWorldPosition(cachedDecorOffset, GetChunkSize());
            
            if (biome != null && Random.value < biome.DecorationChance)
            {
            TileBase decoration = biome.GetRandomDecorationTile();
            if (decoration != null)
            {
                // Copy the cached values into the array
                positions[decorCount].x = cachedDecorOffset.x;
                positions[decorCount].y = cachedDecorOffset.y;
                positions[decorCount].z = cachedDecorOffset.z;
                tiles[decorCount] = decoration;
                decorCount++;
            }
            }
        }
        }
        
        if (decorCount > 0)
        {
        // Set tiles individually to avoid any allocations
        // This is slightly slower but completely GC-free
        for (int i = 0; i < decorCount; i++)
        {
            decorationTilemap.SetTile(positions[i], tiles[i]);
        }
        }
    }


    void UpdateActiveBounds()
    {
        // Use cached Vector3 instances to avoid allocations
        cachedMinBounds.x = (currentPlayerChunk.x - viewDistance) * chunkWidth;
        cachedMinBounds.y = (currentPlayerChunk.y - viewDistance) * chunkHeight;
        cachedMinBounds.z = 0;
        
        cachedMaxBounds.x = (currentPlayerChunk.x + viewDistance + 1) * chunkWidth;
        cachedMaxBounds.y = (currentPlayerChunk.y + viewDistance + 1) * chunkHeight;
        cachedMaxBounds.z = 0;

        // Modify existing Bounds instead of creating new one
        activeBounds.SetMinMax(cachedMinBounds, cachedMaxBounds);
    }

    void ClearDistantTiles()
    {
        // Clear chunks that are beyond view distance + buffer
        // This creates a buffer zone where chunks remain loaded even after leaving view distance
        // Chunks are only unloaded when player moves far enough away, reducing pop-in/pop-out
        chunksToClearCache.Clear();
        
        // Calculate unload distance (view distance + buffer)
        int unloadDistance = viewDistance + unloadBufferDistance;

        foreach (var kvp in generatedChunks)
        {
            ChunkCoordinate chunkCoord = kvp.Key;
            
            // Calculate Manhattan distance from player chunk
            int distanceX = Mathf.Abs(chunkCoord.x - currentPlayerChunk.x);
            int distanceY = Mathf.Abs(chunkCoord.y - currentPlayerChunk.y);
            int maxDistance = Mathf.Max(distanceX, distanceY);
            
            // Only unload chunks beyond the unload distance
            if (maxDistance > unloadDistance)
            {
                chunksToClearCache.Add(chunkCoord);
            }
        }

        foreach (var coord in chunksToClearCache)
        {
            ClearChunk(coord);
        }
    }

    void ClearChunk(ChunkCoordinate coord)
    {
        // Defensive check: Ensure the cached empty tile array is the correct size.
        // This handles cases where chunk dimensions might be changed in the editor during runtime.
        if (emptyChunkTileArray == null || emptyChunkTileArray.Length != chunkWidth * chunkHeight)
        {
        emptyChunkTileArray = new TileBase[chunkWidth * chunkHeight];
        }
        
        // Use cached BoundsInt and Vector3Int to avoid allocations
        cachedClearChunkPos.x = coord.x * chunkWidth;
        cachedClearChunkPos.y = coord.y * chunkHeight;
        cachedClearChunkPos.z = 0;
        cachedClearChunkSize.x = chunkWidth;
        cachedClearChunkSize.y = chunkHeight;
        cachedClearChunkSize.z = 1;
        cachedClearChunkBounds.position = cachedClearChunkPos;
        cachedClearChunkBounds.size = cachedClearChunkSize;

        // Clear tiles by setting a block of nulls, which is much faster than one-by-one.
        groundTilemap.SetTilesBlock(cachedClearChunkBounds, emptyChunkTileArray);
        decorationTilemap.SetTilesBlock(cachedClearChunkBounds, emptyChunkTileArray);
        if (collisionTilemap != null)
        {
        collisionTilemap.SetTilesBlock(cachedClearChunkBounds, emptyChunkTileArray);
        }

        generatedChunks.Remove(coord);
        
        if (chunkBorders.ContainsKey(coord))
        {
        Destroy(chunkBorders[coord]);
        chunkBorders.Remove(coord);
        }

        // Deactivate, aber nicht zerstören, das SplatterArea-Objekt dieses Chunks.
        if (chunkSplatterAreas.TryGetValue(coord, out var splatter))
        {
        if (splatter != null)
        {
            splatter.SetActive(false);
        }
        }
        
        // Notify vegetation spawn manager
        if (vegetationSpawnManager != null)
        {
            vegetationSpawnManager.OnChunkUnloaded(coord);
        }

        // Logging removed for performance
    }

    void CreateChunkBorder(ChunkCoordinate coord)
    {
        if (chunkBorders.ContainsKey(coord)) return;

        chunkBorderNameBuilder.Clear();
        chunkBorderNameBuilder.Append("ChunkBorder_");
        chunkBorderNameBuilder.Append(coord.x);
        chunkBorderNameBuilder.Append('_');
        chunkBorderNameBuilder.Append(coord.y);
        GameObject border = new GameObject(chunkBorderNameBuilder.ToString());
        border.transform.SetParent(transform);
        
        Vector3 chunkPos = coord.ToWorldPosition(chunkWidth, chunkHeight);
        border.transform.position = chunkPos + new Vector3(chunkWidth / 2f, chunkHeight / 2f, 0);

        LineRenderer lineRenderer = border.AddComponent<LineRenderer>();
        lineRenderer.positionCount = 5;
        lineRenderer.loop = true;
        lineRenderer.startWidth = 0.1f;
        lineRenderer.endWidth = 0.1f;
        
        // Create material only once
        if (chunkBorderMaterial == null)
        {
        chunkBorderMaterial = new Material(Shader.Find("Sprites/Default"));
        }
        lineRenderer.material = chunkBorderMaterial;
        
        // Different color for current player chunk
        Color borderColor = (coord == currentPlayerChunk) 
        ? new Color(0f, 1f, 0f, 0.8f)  // Green for player chunk
        : new Color(1f, 1f, 0f, 0.5f); // Yellow for other chunks
        
        lineRenderer.startColor = borderColor;
        lineRenderer.endColor = borderColor;
        lineRenderer.sortingOrder = 100;

        float halfWidth = chunkWidth / 2f;
        float halfHeight = chunkHeight / 2f;
        
        // Use cached array to avoid allocation
        chunkBorderPositions[0].Set(-halfWidth, -halfHeight, 0);
        chunkBorderPositions[1].Set(halfWidth, -halfHeight, 0);
        chunkBorderPositions[2].Set(halfWidth, halfHeight, 0);
        chunkBorderPositions[3].Set(-halfWidth, halfHeight, 0);
        chunkBorderPositions[4].Set(-halfWidth, -halfHeight, 0);

        lineRenderer.SetPositions(chunkBorderPositions);
        chunkBorders[coord] = border;
    }

    public TileBase GetTileAt(Vector3 worldPosition, TilemapType tilemapType)
    {
        Vector3Int cellPos = grid.WorldToCell(worldPosition);
        
        switch (tilemapType)
        {
        case TilemapType.Ground:
            return groundTilemap.GetTile(cellPos);
        case TilemapType.Decoration:
            return decorationTilemap.GetTile(cellPos);
        case TilemapType.Collision:
            return collisionTilemap?.GetTile(cellPos);
        default:
            return null;
        }
    }

    public void SetTileAt(Vector3 worldPosition, TileBase tile, TilemapType tilemapType)
    {
        Vector3Int cellPos = grid.WorldToCell(worldPosition);
        
        switch (tilemapType)
        {
        case TilemapType.Ground:
            groundTilemap.SetTile(cellPos, tile);
            break;
        case TilemapType.Decoration:
            decorationTilemap.SetTile(cellPos, tile);
            break;
        case TilemapType.Collision:
            collisionTilemap?.SetTile(cellPos, tile);
            break;
        }
    }
    
    public int GetChunkSize()
    {
        // Return the larger dimension for compatibility
        return Mathf.Max(chunkWidth, chunkHeight);
    }
    
    public int GetChunkWidth()
    {
        return chunkWidth;
    }
    
    public int GetChunkHeight()
    {
        return chunkHeight;
    }
    
    public BiomeManager GetBiomeManager()
    {
        return biomeManager;
    }
    
    public void ForceUpdateChunks()
    {
        UpdateVisibleChunks();
    }
    
    public ChunkCoordinate GetCurrentPlayerChunk()
    {
        return currentPlayerChunk;
    }
    
    public Bounds GetChunkBounds(ChunkCoordinate chunk)
    {
        Vector3 chunkWorldPos = chunk.ToWorldPosition(chunkWidth, chunkHeight);
        Vector3 center = chunkWorldPos + new Vector3(chunkWidth / 2f, chunkHeight / 2f, 0);
        Vector3 size = new Vector3(chunkWidth, chunkHeight, 0);
        return new Bounds(center, size);
    }
    
    public Dictionary<ChunkCoordinate, GameObject> GetChunkSplatterAreas()
    {
        return chunkSplatterAreas;
    }
    
    void UpdateChunkBorders(HashSet<ChunkCoordinate> visibleChunks)
    {
        // Remove borders that are no longer visible (re-using a cached list to avoid allocations).
        chunkBordersToRemove.Clear();

        foreach (var kvp in chunkBorders)
        {
        if (!visibleChunks.Contains(kvp.Key))
        {
            Destroy(kvp.Value);
            chunkBordersToRemove.Add(kvp.Key);
        }
        }

        foreach (var coord in chunkBordersToRemove)
        {
        chunkBorders.Remove(coord);
        }

        // Create or refresh borders for the currently visible chunks.
        foreach (var coord in visibleChunks)
        {
        if (!chunkBorders.ContainsKey(coord))
        {
            CreateChunkBorder(coord);
        }
        else
        {
            // Update color for existing borders
            LineRenderer lr = chunkBorders[coord].GetComponent<LineRenderer>();
            if (lr != null)
            {
            Color borderColor = (coord == currentPlayerChunk)
                ? new Color(0f, 1f, 0f, 0.8f)  // Green for player chunk
                : new Color(1f, 1f, 0f, 0.5f); // Yellow for other chunks
            lr.startColor = borderColor;
            lr.endColor = borderColor;
            }
        }
        }
    }

    void UpdateChunkSplatterAreas(HashSet<ChunkCoordinate> visibleChunks)
    {
        // Only keep the splatter area active for the current player chunk
        // 1. Deactivate all splatter areas except the current player chunk
        foreach (var kvp in chunkSplatterAreas)
        {
        if (kvp.Key != currentPlayerChunk)
        {
            if (kvp.Value != null && kvp.Value.activeSelf)
            {
            kvp.Value.SetActive(false);
            
            // Optionally clear splatter content when deactivating
            if (clearSplatterOnDeactivate)
            {
                var splatterArea = kvp.Value.GetComponent<SplatterArea>();
                if (splatterArea != null)
                {
                splatterArea.ResetCanvasTexture();
                }
            }
            }
        }
        }

        // 2. Activate/Create splatter area only for the current player chunk
        if (chunkSplatterAreas.TryGetValue(currentPlayerChunk, out var area))
        {
        if (area != null && !area.activeSelf)
        {
            area.SetActive(true);
        }
        }
        else
        {
        // Create splatter area for current chunk if it doesn't exist yet
        if (generatedChunks.ContainsKey(currentPlayerChunk))
        {
            CreateSplatterArea(currentPlayerChunk);
        }
        }
        
        // Debug logging
        if (debugSplatterAreas)
        {
        int activeCount = 0;
        foreach (var kvp in chunkSplatterAreas)
        {
            if (kvp.Value != null && kvp.Value.activeSelf)
            {
            activeCount++;
            }
        }
        Debug.Log($"[SplatterAreas] Active: {activeCount}, Total: {chunkSplatterAreas.Count}, Current Chunk: {currentPlayerChunk}");
        }
    }

    public void CreateSplatterArea(ChunkCoordinate coord)
    {
        // Vermeide doppelte Instanzen
        if (chunkSplatterAreas.TryGetValue(coord, out var existingArea))
        {
        if (existingArea != null)
        {
            existingArea.SetActive(true);
            return;
        }
        }

        GameObject splatterArea = Instantiate(splatterAreaPrefab, transform);
        // Position splatter area at chunk center
        Vector3 chunkWorldPos = coord.ToWorldPosition(chunkWidth, chunkHeight);
        Vector3 centerPos = chunkWorldPos + new Vector3(chunkWidth * 0.5f, chunkHeight * 0.5f, 0f);
        splatterArea.transform.position = centerPos;

        // Ensure uniform scale
        splatterArea.transform.localScale = Vector3.one;

        // Adjust RectTransform size to match chunk dimensions
        RectTransform rt = splatterArea.GetComponent<RectTransform>();
        if (rt != null)
        {
        rt.sizeDelta = new Vector2(chunkWidth, chunkHeight);
        }

        chunkSplatterAreas[coord] = splatterArea;
    }

    #if UNITY_EDITOR
    [Button("Toggle Chunk Borders", ButtonSizes.Large)]
    void ToggleChunkBorders()
    {
        showChunkBorders = !showChunkBorders;
        
        if (showChunkBorders)
        {
        foreach (var kvp in generatedChunks)
        {
            if (!chunkBorders.ContainsKey(kvp.Key))
            {
            CreateChunkBorder(kvp.Key);
            }
        }
        }
        else
        {
        foreach (var border in chunkBorders.Values)
        {
            Destroy(border);
        }
        chunkBorders.Clear();
        }
    }

    [Button("Regenerate World", ButtonSizes.Large)]
    void RegenerateWorld()
    {
        regenerateWorldChunksCache.Clear();
        regenerateWorldChunksCache.AddRange(generatedChunks.Keys);
        foreach (var coord in regenerateWorldChunksCache)
        {
        ClearChunk(coord);
        }

        worldSeed = Random.Range(0, int.MaxValue);
        biomeManager.Initialize(worldSeed);
        totalChunksGenerated = 0;
        
        GenerateChunk(currentPlayerChunk);
        UpdateVisibleChunks();
    }

    [Button("Clear All Tiles", ButtonSizes.Large)]
    void ClearAllTiles()
    {
        groundTilemap.CompressBounds();
        decorationTilemap.CompressBounds();
        collisionTilemap?.CompressBounds();
        
        groundTilemap.GetComponent<TilemapRenderer>().enabled = false;
        decorationTilemap.GetComponent<TilemapRenderer>().enabled = false;
        
        groundTilemap.RefreshAllTiles();
        decorationTilemap.RefreshAllTiles();
        
        groundTilemap.GetComponent<TilemapRenderer>().enabled = true;
        decorationTilemap.GetComponent<TilemapRenderer>().enabled = true;
        
        generatedChunks.Clear();
        totalChunksGenerated = 0;
        
        // Clear prefab vegetation
        if (vegetationSpawnManager != null)
        {
            vegetationSpawnManager.ClearAllVegetation();
        }
    }

    [Button("Log Chunk Info", ButtonSizes.Medium)]
    void LogChunkInfo()
    {
        logInfoBuilder.Clear();
        logInfoBuilder.AppendLine("Tilemap Chunk Manager Info:");
        logInfoBuilder.Append("- Total chunks generated: ").AppendLine(totalChunksGenerated.ToString());
        logInfoBuilder.Append("- Currently tracked: ").AppendLine(generatedChunks.Count.ToString());
        logInfoBuilder.Append("- Player chunk: ").AppendLine(currentPlayerChunk.ToString());
        logInfoBuilder.Append("- Active bounds: ").AppendLine(activeBounds.ToString());
        logInfoBuilder.Append("- Ground tiles count: ").AppendLine(groundTilemap.GetUsedTilesCount().ToString());
        logInfoBuilder.Append("- Decoration tiles count: ").AppendLine(decorationTilemap.GetUsedTilesCount().ToString());
        
        Debug.Log(logInfoBuilder.ToString());
    }

    [Button("Toggle 2D Biome Distribution", ButtonSizes.Medium)]
    void Toggle2DBiomeDistribution()
    {
        // This would need to be exposed in BiomeManager
        Debug.Log("To toggle 2D biome distribution, change the 'Use 2D Biome Distribution' setting in the Biome Manager and regenerate the world.");
    }

    [Button("Validate 2D Biome Setup", ButtonSizes.Medium)]
    void Validate2DBiomeSetup()
    {
        Debug.Log("=== 2D Biome Setup Validation ===");
        
        // Check if 2D mode is enabled
        bool is2D = biomeManager.IsUse2DBiomeDistribution();
        Debug.Log($"2D Biome Distribution Enabled: {(is2D ? "<color=green>YES</color>" : "<color=red>NO</color>")}");
        
        if (!is2D)
        {
        Debug.LogWarning("Enable 'Use 2D Biome Distribution' in BiomeManager to use 2D transitions!");
        return;
        }
        
        // Check biomes
        var biomes = biomeManager.GetAllBiomes();
        Debug.Log($"Total Biomes: {biomes.Count}");
        
        foreach (var biome in biomes)
        {
        if (biome == null) continue;
        
        Debug.Log($"\nBiome: {biome.BiomeName}");
        Debug.Log($"  - Top tiles: {(biome.TopTransitionTiles?.Length ?? 0)}");
        Debug.Log($"  - Bottom tiles: {(biome.BottomTransitionTiles?.Length ?? 0)}");
        Debug.Log($"  - Left tiles: {(biome.LeftTransitionTiles?.Length ?? 0)}");
        Debug.Log($"  - Right tiles: {(biome.RightTransitionTiles?.Length ?? 0)}");
        Debug.Log($"  - NE corner tiles: {(biome.NorthEastCornerTiles?.Length ?? 0)}");
        Debug.Log($"  - NW corner tiles: {(biome.NorthWestCornerTiles?.Length ?? 0)}");
        Debug.Log($"  - SE corner tiles: {(biome.SouthEastCornerTiles?.Length ?? 0)}");
        Debug.Log($"  - SW corner tiles: {(biome.SouthWestCornerTiles?.Length ?? 0)}");
        
        if ((biome.TopTransitionTiles?.Length ?? 0) == 0 &&
            (biome.BottomTransitionTiles?.Length ?? 0) == 0 &&
            (biome.LeftTransitionTiles?.Length ?? 0) == 0 &&
            (biome.RightTransitionTiles?.Length ?? 0) == 0)
        {
            Debug.LogWarning($"  <color=red>WARNING: No transition tiles configured!</color>");
        }
        }
        
        // Check current position
        Vector3Int playerWorldPos = new Vector3Int(
        Mathf.FloorToInt(playerTransform.position.x),
        Mathf.FloorToInt(playerTransform.position.y),
        0
        );
        
        Debug.Log($"\nPlayer Position: {playerWorldPos}");
        bool atBoundary = biomeManager.IsAtBiomeBoundary2D(playerWorldPos, GetChunkSize());
        Debug.Log($"At Boundary: {(atBoundary ? "<color=green>YES</color>" : "<color=red>NO</color>")}");
        
        if (atBoundary)
        {
        var directions = biomeManager.GetTransitionDirections2D(playerWorldPos, GetChunkSize());
        Debug.Log($"Transition Directions: {directions}");
        }
        
        Debug.Log("\nLog Chunk Operations: <color=yellow>DISABLED - Logging removed for performance</color>");
        Debug.Log("=================================");
    }

    [Button("Debug Splatter Areas", ButtonSizes.Medium)]
    void DebugSplatterAreas()
    {
        Debug.Log("=== Splatter Area Status ===");
        Debug.Log($"Current Player Chunk: {currentPlayerChunk}");
        Debug.Log($"Total Splatter Areas: {chunkSplatterAreas.Count}");
        Debug.Log($"Clear on Deactivate: {clearSplatterOnDeactivate}");
        
        int activeCount = 0;
        foreach (var kvp in chunkSplatterAreas)
        {
        if (kvp.Value != null && kvp.Value.activeSelf)
        {
            activeCount++;
            Debug.Log($"  - Active: Chunk {kvp.Key} at {kvp.Value.transform.position}");
        }
        }
        
        Debug.Log($"Active Splatter Areas: {activeCount}");
        
        if (activeCount == 0)
        {
        Debug.LogWarning("No active splatter areas! Make sure splatterAreaPrefab is assigned.");
        }
        else if (activeCount > 1)
        {
        Debug.LogWarning($"Multiple active splatter areas ({activeCount})! Only current chunk should be active.");
        }
        
        Debug.Log("===========================");
    }
    
    [Button("Debug Vegetation System", ButtonSizes.Medium)]
    void DebugVegetationSystem()
    {
        Debug.Log("=== Vegetation System Debug ===");
        
        // Check prefab vegetation system
        Debug.Log($"VegetationSpawnManager: {(vegetationSpawnManager != null ? "<color=green>Exists</color>" : "<color=red>Missing</color>")}");
        
        if (vegetationSpawnManager != null)
        {
            vegetationSpawnManager.DebugLogStats();
        }
        
        // Check current chunk's biome vegetation settings
        Vector3Int playerWorldPos = new Vector3Int(
            Mathf.FloorToInt(playerTransform.position.x),
            Mathf.FloorToInt(playerTransform.position.y),
            0
        );
        
        BiomeData currentBiome = biomeManager.GetBiomeForWorldPosition(playerWorldPos, GetChunkSize());
        if (currentBiome != null)
        {
            Debug.Log($"\nCurrent Biome: {currentBiome.BiomeName}");
            Debug.Log($"Has Vegetation: {(currentBiome.HasVegetation() ? "<color=green>YES</color>" : "<color=red>NO</color>")}");
            
            if (currentBiome.HasVegetation())
            {
                Debug.Log($"- High Grass Sprites: {currentBiome.HighGrassVegetationSprites?.Length ?? 0}");
                Debug.Log($"- Low Grass Sprites: {currentBiome.LowGrassVegetationSprites?.Length ?? 0}");
                Debug.Log($"- Vegetation Density: {currentBiome.VegetationDensity:P0}");
                Debug.Log($"- High Grass Ratio: {currentBiome.HighGrassRatio:P0}");
                Debug.Log($"- Use Clustering: {currentBiome.UseVegetationClustering}");
                
                if (currentBiome.UseVegetationClustering)
                {
                    Debug.Log($"  - Noise Scale: {currentBiome.VegetationNoiseScale}");
                    Debug.Log($"  - Noise Threshold: {currentBiome.VegetationNoiseThreshold}");
                    
                    // Test noise value at player position
                    float noiseValue = Mathf.PerlinNoise(
                        (playerWorldPos.x * currentBiome.VegetationNoiseScale) + currentBiome.VegetationNoiseOffset.x,
                        (playerWorldPos.y * currentBiome.VegetationNoiseScale) + currentBiome.VegetationNoiseOffset.y
                    );
                    Debug.Log($"  - Noise at player pos: {noiseValue:F3} {(noiseValue >= currentBiome.VegetationNoiseThreshold ? "(vegetation allowed)" : "(no vegetation)")}");
                }
            }
        }
        
        // Check all visible chunks for biome variety
        Debug.Log("\nVisible Chunks Biome Vegetation Status:");
        HashSet<ChunkCoordinate> visibleChunks = GetVisibleChunks();
        Dictionary<string, int> biomeVegetationCount = new Dictionary<string, int>();
        
        foreach (var chunk in visibleChunks)
        {
            Vector3 chunkWorldPosFloat = chunk.ToWorldPosition(chunkWidth, chunkHeight);
            Vector3Int chunkCenterPos = new Vector3Int(
                Mathf.FloorToInt(chunkWorldPosFloat.x + chunkWidth / 2f),
                Mathf.FloorToInt(chunkWorldPosFloat.y + chunkHeight / 2f),
                0
            );
            BiomeData chunkBiome = biomeManager.GetBiomeForWorldPosition(chunkCenterPos, GetChunkSize());
            
            if (chunkBiome != null)
            {
                string key = $"{chunkBiome.BiomeName} ({(chunkBiome.HasVegetation() ? "has vegetation" : "no vegetation")})";
                if (!biomeVegetationCount.ContainsKey(key))
                    biomeVegetationCount[key] = 0;
                biomeVegetationCount[key]++;
            }
        }
        
        foreach (var kvp in biomeVegetationCount)
        {
            Debug.Log($"- {kvp.Key}: {kvp.Value} chunks");
        }
        
        Debug.Log("===========================");
    }
    
    [Button("Debug Transition Zones", ButtonSizes.Medium)]
    void DebugTransitionZones()
    {
        Vector3Int playerWorldPos = new Vector3Int(
        Mathf.FloorToInt(playerTransform.position.x),
        Mathf.FloorToInt(playerTransform.position.y),
        0
        );
        
        // Get biome at player position (tile-based)
        BiomeData currentBiome = biomeManager.GetBiomeForWorldPosition(playerWorldPos, GetChunkSize());
        
        Debug.Log($"Player World Pos: {playerWorldPos}");
        Debug.Log($"Current Chunk: {currentPlayerChunk}");
        Debug.Log($"Current Biome: {currentBiome?.BiomeName ?? "None"}");
        Debug.Log($"2D Biome Distribution: {biomeManager.IsUse2DBiomeDistribution()}");
        
        if (biomeManager.IsUse2DBiomeDistribution())
        {
        // 2D biome debug
        bool atBoundary = biomeManager.IsAtBiomeBoundary2D(playerWorldPos, GetChunkSize());
        Debug.Log($"At 2D Biome Boundary: {atBoundary}");
        
        if (atBoundary)
        {
            TransitionDirection directions = biomeManager.GetTransitionDirections2D(playerWorldPos, GetChunkSize());
            Debug.Log($"Transition Directions: {directions}");
            
            List<BiomeData> neighbors = biomeManager.GetNeighboringBiomes2D(playerWorldPos, GetChunkSize());
            string neighborNames = string.Join(", ", neighbors.ConvertAll(b => b.BiomeName));
            Debug.Log($"Neighboring Biomes: {neighborNames}");
            
            // Analyze transition
            var analysis = TransitionTileGenerator.AnalyzeTransitionAt(playerWorldPos, biomeManager, GetChunkSize());
            Debug.Log($"Transition Analysis - Directions: {analysis.TransitionDirections}");
        }
        }
        else
        {
        // 1D biome debug
        bool atBoundary = biomeManager.IsExactlyAtBiomeBoundary(playerWorldPos.x, GetChunkSize());
        Debug.Log($"At Biome Boundary: {atBoundary}");
        
        if (atBoundary)
        {
            BiomeData sourceBiome = biomeManager.GetSourceBiomeForBoundary(playerWorldPos.x, GetChunkSize());
            bool useLeftPointing = biomeManager.IsBoundaryTransitionFromLeft(playerWorldPos.x, GetChunkSize());
            Debug.Log($"Source Biome: {sourceBiome?.BiomeName ?? "None"}, Use Left-Pointing Tile: {useLeftPointing}");
        }
        
        // Log biome boundaries
        int biomeWidthInTiles = biomeManager.GetBiomeWidthInChunks() * chunkWidth;
        Debug.Log($"Biome width in tiles: {biomeWidthInTiles}");
        
        for (int i = -2; i <= 2; i++)
        {
            int boundaryX = i * biomeWidthInTiles;
            Debug.Log($"Biome boundary at X: {boundaryX}");
        }
        }
        
        Debug.Log("Transition tiles enabled: Log Chunk Operations = DISABLED (Logging removed for performance)");
    }

    void OnDrawGizmosSelected()
    {
        if (!Application.isPlaying) return;

        Gizmos.color = Color.green;
        Vector3 playerChunkPos = currentPlayerChunk.ToWorldPosition(chunkWidth, chunkHeight);
        Vector3 chunkCenter = playerChunkPos + new Vector3(chunkWidth / 2f, chunkHeight / 2f, 0);
        Gizmos.DrawWireCube(chunkCenter, new Vector3(chunkWidth, chunkHeight, 0));

        Gizmos.color = new Color(1f, 1f, 0f, 0.3f);
        Gizmos.DrawWireCube(activeBounds.center, activeBounds.size);

        // Draw unload boundary (view distance + buffer)
        Gizmos.color = Color.red;
        int unloadDistance = viewDistance + unloadBufferDistance;
        float unloadSizeX = chunkWidth * (unloadDistance * 2 + 1);
        float unloadSizeY = chunkHeight * (unloadDistance * 2 + 1);
        Gizmos.DrawWireCube(chunkCenter, new Vector3(unloadSizeX, unloadSizeY, 0));
        
        // Draw buffer zone boundary for clarity
        Gizmos.color = new Color(1f, 0.5f, 0f, 0.5f); // Orange
        float bufferSizeX = chunkWidth * ((viewDistance + 1) * 2 + 1);
        float bufferSizeY = chunkHeight * ((viewDistance + 1) * 2 + 1);
        Gizmos.DrawWireCube(chunkCenter, new Vector3(bufferSizeX, bufferSizeY, 0));
        
        // Draw biome boundaries
        if (biomeManager != null)
        {
        if (biomeManager.IsUse2DBiomeDistribution())
        {
            // Draw 2D biome chunks with different colors
            HashSet<ChunkCoordinate> visibleChunks = GetVisibleChunks();
            Dictionary<BiomeData, Color> biomeColors = new Dictionary<BiomeData, Color>();
            Color[] availableColors = { Color.red, Color.blue, Color.green, Color.yellow, Color.cyan, Color.magenta };
            int colorIndex = 0;
            
            foreach (var chunk in visibleChunks)
            {
            BiomeData biome = biomeManager.GetBiomeForChunk(chunk);
            if (biome != null)
            {
                if (!biomeColors.ContainsKey(biome))
                {
                biomeColors[biome] = availableColors[colorIndex % availableColors.Length];
                colorIndex++;
                }
                
                Gizmos.color = new Color(biomeColors[biome].r, biomeColors[biome].g, biomeColors[biome].b, 0.2f);
                Vector3 chunkPos = chunk.ToWorldPosition(chunkWidth, chunkHeight);
                Vector3 chunkCenterPos = chunkPos + new Vector3(chunkWidth / 2f, chunkHeight / 2f, 0);
                Gizmos.DrawCube(chunkCenterPos, new Vector3(chunkWidth * 0.9f, chunkHeight * 0.9f, 0));
                
                // Draw biome name in Scene view
                #if UNITY_EDITOR
                UnityEditor.Handles.Label(chunkCenterPos, biome.BiomeName);
                #endif
            }
            }
        }
        else
        {
            // Draw 1D biome boundaries
            Gizmos.color = Color.magenta;
            int biomeWidthInTiles = biomeManager.GetBiomeWidthInChunks() * chunkWidth;
            
            // Draw visible biome boundaries
            for (int i = -5; i <= 5; i++)
            {
            float boundaryX = i * biomeWidthInTiles;
            
            // Only draw if within view
            if (Mathf.Abs(boundaryX - playerChunkPos.x) < unloadSizeX / 2f)
            {
                Vector3 bottomPoint = new Vector3(boundaryX, playerChunkPos.y - unloadSizeY / 2f, 0);
                Vector3 topPoint = new Vector3(boundaryX, playerChunkPos.y + unloadSizeY / 2f, 0);
                Gizmos.DrawLine(bottomPoint, topPoint);
                
                // Draw boundary marker
                Gizmos.DrawWireCube(new Vector3(boundaryX, playerChunkPos.y, 0), new Vector3(2f, 10f, 0));
            }
            }
        }
        }
    }
    #endif
    }
    
    public enum TilemapType
    {
    Ground,
    Decoration,
    Collision
}