# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
Unity-based 2D top-down rogue-like inspired by Path of Exile with procedural world generation, gem-based skill system, and complex character progression.

**Unity Version**: 6000.2.0b3 | **Pipeline**: URP | **IDE**: VS Code | **Input System**: Unity Input System (new)


🔁 Wiedervermeidung / Klarheit
DRY – Don't Repeat Yourself
→ Wiederhole Code nicht. Extrahiere Wiederholungen in Funktionen, Klassen oder Module.

KISS – Keep It Simple, Stupid
→ Schreibe Code so einfach wie möglich. Vermeide unnötige Komplexität.

YAGNI – You Ain't Gonna Need It
→ Implementiere nur das, was du aktuell brauchst – kein Overengineering.

SoC – Separation of Concerns
→ Teile Code in klar abgegrenzte Verantwortlichkeiten (z. B. Daten, Logik, Darstellung).

🔄 Änderbarkeit / Wartbarkeit
SRP – Single Responsibility Principle (Teil von SOLID)
→ Jede Klasse/Funktion sollte nur eine einzige Aufgabe haben.

OCP – Open/Closed Principle
→ Code sollte offen für Erweiterung, aber geschlossen für Veränderung sein.

LSP – Liskov Substitution Principle
→ Subklassen sollten ohne Probleme überall einsetzbar sein, wo ihre Basisklasse verwendet wird.

ISP – Interface Segregation Principle
→ Verwende viele kleine, spezifische Interfaces statt eines großen.

DIP – Dependency Inversion Principle
→ High-Level-Module sollten nicht von Low-Level-Modulen abhängen; beide sollen von Abstraktionen abhängen.
## High-Level Architecture

### Core Systems & Data Flow
```
Player Input → PlayerController → SkillExecutor → GemSocketController
                                        ↓
                                  PoolManager ← Projectile/Effect
                                        ↓
                              CollisionManager → Damage Application
```

### Key Architectural Patterns
1. **Data-Driven Design**: ScriptableObjects for all game data (gems, biomes, items, enemy configs)
2. **Instance Pattern**: Runtime instances (GemInstance) separate from templates (GemData)
3. **Manager Singletons**: GemManager (source of truth), PoolManager (object pooling), ChunkBuffSystem (attack modifiers)
4. **Event-Driven UI**: No polling, UI subscribes to data change events
5. **Spatial Partitioning**: Custom collision system using SpatialHashGrid
6. **Strategy Pattern**: Enemy attack systems (IAttackStrategy, MeleeAttackStrategy, RangedAttackStrategy)
7. **Component-Based Enemies**: Separation of movement, animation, and attack logic
8. **Modifier System**: Stackable attack modifiers with source tracking

### Critical System Dependencies
- **ChunkBasedGraphMover** must update BEFORE **ChunkContentSpawner** (agents need valid pathfinding graph)
- **TilemapChunkManager** drives world generation → biome placement → tile transitions (singleton pattern)
- **GemManager** is the single source of truth for all gem state (inventory/equipped)
- **PoolManager** handles ALL spawnable objects (projectiles, enemies, effects)
- **Attack Strategies** require timeout fallbacks since OnAnimationCompleted events are unreliable
- **EnemyAnimationController** must receive velocity updates from movement systems for proper animation states
- **ChunkBuffSystem** manages chunk-based attack modifiers (singleton pattern)

## Development Guidelines

### Performance Requirements (STRICT)
- **Zero-GC Gameplay**: Use object pooling, no `new` in Update loops
- **Spatial Optimization**: All entities use SpatialCollider for collision
- **Chunk-Based Loading**: World unloads distant chunks automatically
- **Particle Effects**: Use `Emit()` NEVER `Instantiate()`
- **Collision Queries**: Cache results, use spatial partitioning
- **Enemy Updates**: Staggered state updates (0.5s intervals)

### Code Organization
- **Namespaces**: Do not create custom ones
- **Always include**: `using Sirenix.OdinInspector;` for editor attributes
- **Component Pattern**: Single responsibility, interface-based communication

### Unity-Specific Requirements
- Never modify .meta files
- Use `[RequireComponent]` for dependencies
- Cache references in Awake/Start
- Use ScriptableObjects for game data
- Unsubscribe all events in OnDestroy/OnReturnToPool

## Critical Setup & Common Issues

### Scene Requirements
Every scene MUST have:
- TilemapChunkManager (with Grid component)
- CollisionManager (references TilemapChunkManager)
- PoolManager
- GemManager
- ChunkBasedGraphMover (for agent pathfinding)
- ChunkContentSpawner (references ChunkBasedGraphMover, uses EnemySpawnConfiguration)
- ChunkBuffSystem (optional, for attack modifier scaling)

### Enemy Setup Requirements
Every enemy prefab MUST have:
- BaseEnemy-derived script (PathfindingEnemy/SimpleEnemy)
- CombatantHealth component
- SpatialCollider (for collision detection)
- Attack strategy assigned in BaseEnemy
- For PathfindingEnemy: FollowerEntity, EnemyAnimationController, EnemyChunkBoundaryEnforcer, EnemyDebugVisualizer
- SpriteAnimator + SpriteRenderer for animation



## Key Files & Their Roles
- `GemManager.cs`: Central authority for gem state, handles equip/unequip
- `GemInstance.cs`: Runtime gem with level/quality/modifiers
- `TilemapChunkManager.cs`: Procedural world generation, chunk loading (singleton)
- `SpatialHashGrid.cs`: Custom collision detection system
- `PoolManager.cs`: Object pooling for all spawnable entities
- `ChunkContentSpawner.cs`: Race-based enemy spawning with weighted selection
- `ChunkBuffSystem.cs`: Distance-based attack modifiers and elite zones
- `AttackModifier.cs`: Attack modification system with damage/crit/projectile modifiers

### Enemy System Architecture
- `BaseEnemy.cs`: Abstract base with attack configuration, health, targeting, chunk buff integration
- `PathfindingEnemy.cs`: A* pathfinding implementation using FollowerEntity
- `SimpleEnemy.cs`: Direct vector-based movement for simple agents
- `PathfindingMovement.cs`: State machine (Idle/Patrol/Chase/Attack) with cooldown management
- `EnemyAnimationController.cs`: Velocity-based animation transitions (idle/move/chase speeds)
- `IAttackStrategy.cs`: Interface for attack implementations with frame-based timing and modifier support
- `MeleeAttackStrategy.cs`: Contact damage with animation events, attack speed modifiers
- `RangedAttackStrategy.cs`: Projectile spawning with animation events, multi-projectile support
- `AttackModifierCollection.cs`: Manages and calculates attack modifiers (damage, crit, projectiles)
- `EnemySpawnConfiguration.cs`: ScriptableObject for race-based spawning with level scaling
- `EnemyRaceData.cs`: ScriptableObject defining enemy race properties and prefab variants

### Animation System & Frame Events
- `SpriteAnimator.cs`: Component for playing SpriteAnimation assets with event system and speed modifiers
- `SpriteAnimation.cs`: ScriptableObject with frames + frame events for precise timing
- `SpriteAnimationTool.cs`: Editor tool for creating animations with frame events
- `AnimationState.cs`: Runtime state manager for animation playback
- **Frame Events Required**: Melee ('hit'/'strike'/'impact'), Ranged ('shoot'/'fire'/'projectile')
- **CRITICAL**: Without frame events, attack animations won't trigger damage
- **Attack Speed Modifiers**: SpriteAnimator supports temporary speed multipliers for attack animations

### ScriptableObject Configurations
- `ChunkBuffConfig`: Configure distance-based attack scaling, elite zones, and random buffs
  - Distance scaling: damage/crit/attack speed bonuses based on chunk distance
  - Elite zones: Special chunks with powerful buffs at regular intervals
  - Random buffs: Weighted random modifiers (multi-shot, berserker, precision, etc.)
- `EnemySpawnConfiguration`: Race-based enemy spawning with level restrictions
  - Race weights and level ranges
  - Multi-race spawn chances
  - Enemy count scaling by distance/level
- `EnemyRaceData`: Individual enemy race definitions
  - Multiple prefab variants per race
  - Race-specific properties and behaviors

## Advanced Debug Features
- **ECS Debugger**: Window → Entities → Systems (pathfinding performance)
- **Visual Debuggers**: Most components have debug visualization toggles
- **State Machine Logging**: Enemy AI state transitions
- **Spatial Grid Overlay**: Collision system visualization via CollisionManager
- **Performance Analysis**: Unity Profiler for GC and frame timing
- **Damage Debug Info**: PlayerStats.TakeDamage() accepts debug strings for damage breakdowns
- **Attack Strategy Debug**: MeleeAttackStrategy has enableDebugLogs for detailed attack logging
- **Chunk Buff Debug**: ChunkBuffSystem shows active buffs and modifier calculations

## Documentation
Detailed documentation in `docs/` folder:
- Architecture overview: `docs/architecture/overview.md`
- System-specific docs in `docs/architecture/`
- Implementation examples in `docs/guides/`
- Developer cheatsheet: `docs/developer-cheatsheet.md`

## Claude Memories
- dont overengineer, follow the prompt
- schreibe nicht ungefragt debug funktionen
- Dont create Helper Create Prefab tools or testers
# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.