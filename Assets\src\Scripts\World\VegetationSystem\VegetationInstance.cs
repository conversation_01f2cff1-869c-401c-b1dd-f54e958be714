using UnityEngine;
using Sirenix.OdinInspector;

[RequireComponent(typeof(SpriteRenderer))]
public class VegetationInstance : MonoBehaviour, ISpawnable
{
    [Title("Vegetation Instance")]
    private Sprite<PERSON>enderer spriteRenderer;
    private MultiVegetationController multiVegetationController;
    private YSortingController ySortingController;

    void Awake()
    {
        spriteRenderer = GetComponent<SpriteRenderer>();
        multiVegetationController = GetComponent<MultiVegetationController>();
        ySortingController = GetComponent<YSortingController>();
    }
    
    /// <summary>
    /// Sets a single sprite (legacy method for backward compatibility)
    /// </summary>
    public void SetSprite(Sprite vegetationSprite)
    {
        if (spriteRenderer != null && vegetationSprite != null)
        {
            spriteRenderer.sprite = vegetationSprite;
        }
    }

    /// <summary>
    /// Sets multiple random sprites using MultiVegetationController if available,
    /// otherwise falls back to single sprite on main SpriteRenderer
    /// </summary>
    public void SetRandomSprites(Sprite[] sprites, Color color, bool flipX = false)
    {
        if (multiVegetationController != null)
        {
            // Use multi-sprite system
            multiVegetationController.SetRandomSprites(sprites, color, flipX);
        }
        else
        {
            // Fallback to single sprite system
            if (sprites != null && sprites.Length > 0 && spriteRenderer != null)
            {
                Sprite randomSprite = sprites[Random.Range(0, sprites.Length)];
                spriteRenderer.sprite = randomSprite;
                spriteRenderer.color = color;
                spriteRenderer.flipX = flipX;
            }
        }
    }

    /// <summary>
    /// Sets color for all sprites (multi-sprite or single sprite)
    /// </summary>
    public void SetColor(Color vegetationColor)
    {
        if (multiVegetationController != null)
        {
            multiVegetationController.SetColor(vegetationColor);
        }
        else if (spriteRenderer != null)
        {
            spriteRenderer.color = vegetationColor;
        }
    }

    /// <summary>
    /// Sets flip state for all sprites (multi-sprite or single sprite)
    /// </summary>
    public void SetFlip(bool flipX)
    {
        if (multiVegetationController != null)
        {
            multiVegetationController.SetFlip(flipX);
        }
        else if (spriteRenderer != null)
        {
            spriteRenderer.flipX = flipX;
        }
    }

    /// <summary>
    /// Sets Y-sorting offset for all sprites (multi-sprite or single sprite)
    /// </summary>
    public void SetYSortingOffset(float yOffset)
    {
        if (multiVegetationController != null)
        {
            multiVegetationController.SetYSortingOffset(yOffset);
        }
        else if (ySortingController != null)
        {
            // Fallback: use cached YSortingController on this GameObject
            ySortingController.SetYOffset(yOffset);
        }
    }
    
    public void OnSpawn()
    {
        // Reset any modifications
        if (multiVegetationController != null)
        {
            multiVegetationController.ClearAllSprites();
        }
        else if (spriteRenderer != null)
        {
            spriteRenderer.flipX = false;
            spriteRenderer.sprite = null; // Clear sprite until assigned
            spriteRenderer.color = Color.white; // Reset color to default
        }
    }
    
    public void OnDespawn()
    {
        // Reset state before returning to pool
        transform.localScale = Vector3.one;
        transform.rotation = Quaternion.identity;

        if (multiVegetationController != null)
        {
            multiVegetationController.ClearAllSprites();
        }
        else if (spriteRenderer != null)
        {
            spriteRenderer.flipX = false;
            spriteRenderer.sprite = null; // Clear sprite reference
            spriteRenderer.color = Color.white; // Reset color to default
        }
    }
}