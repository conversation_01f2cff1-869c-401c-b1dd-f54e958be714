using UnityEngine;
using Sirenix.OdinInspector;

[RequireComponent(typeof(SpriteRenderer))]
public class VegetationInstance : MonoBehaviour, ISpawnable
{
    [Title("Vegetation Instance")]
    private SpriteRenderer spriteRenderer;
    
    void Awake()
    {
        spriteRenderer = GetComponent<SpriteRenderer>();
    }
    
    public void SetSprite(Sprite vegetationSprite)
    {
        if (spriteRenderer != null && vegetationSprite != null)
        {
            spriteRenderer.sprite = vegetationSprite;
        }
    }
    
    public void OnSpawn()
    {
        // Reset any modifications
        if (spriteRenderer != null)
        {
            spriteRenderer.flipX = false;
            spriteRenderer.sprite = null; // Clear sprite until assigned
        }
    }
    
    public void OnDespawn()
    {
        // Reset state before returning to pool
        transform.localScale = Vector3.one;
        transform.rotation = Quaternion.identity;
        
        if (spriteRenderer != null)
        {
            spriteRenderer.flipX = false;
            spriteRenderer.sprite = null; // Clear sprite reference
        }
    }
}