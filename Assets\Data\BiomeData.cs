using UnityEngine;
using UnityEngine.Tilemaps;
using Sirenix.OdinInspector;


    [CreateAssetMenu(fileName = "NewBiome", menuName = "2D Rogue/Biome Data")]
    public class BiomeData : ScriptableObject
    {
        [Title("Biome Settings")]
        [FoldoutGroup("General")]
        [SerializeField]
        private string biomeName = "New Biome";

        [FoldoutGroup("General")]
        [SerializeField, TextArea(3, 5)]
        private string description;

        [FoldoutGroup("General")]
        [SerializeField, ColorUsage(true, false)]
        private Color biomeColor = Color.white;

        [Title("Tiles")]
        [FoldoutGroup("Tiles")]
        [SerializeField]
        [Tooltip("Primary ground tile for this biome")]
        private TileBase primaryGroundTile;

        [FoldoutGroup("Tiles")]
        [SerializeField]
        [Tooltip("Secondary tiles for variation")]
        private TileBase[] secondaryGroundTiles;

        [FoldoutGroup("Tiles")]
        [SerializeField]
        [Tooltip("Decoration tiles (rocks, plants, etc.)")]
        private TileBase[] decorationTiles;

        [FoldoutGroup("Tiles")]
        [SerializeField, Range(0f, 1f)]
        [Tooltip("Chance to place a secondary tile instead of primary")]
        private float secondaryTileChance = 0.4f;

        [FoldoutGroup("Tiles")]
        [SerializeField]
        [Tooltip("Use noise-based distribution for secondary tiles")]
        private bool useNoiseBasedTileSelection = true;

        [FoldoutGroup("Tiles")]
        [SerializeField, Range(0f, 1f)]
        [Tooltip("Chance to place decoration on a tile")]
        private float decorationChance = 0.1f;

        [Title("Transition Tiles")]
        [InfoBox("Left tiles point left (←), used at right biome edge\nRight tiles point right (→), used at left biome edge")]
        [FoldoutGroup("Transition")]
        [SerializeField]
        [Tooltip("Transparent tiles that point to the left (←)")]
        private TileBase[] leftTransitionTiles;

        [FoldoutGroup("Transition")]
        [SerializeField]
        [Tooltip("Transparent tiles that point to the right (→)")]
        private TileBase[] rightTransitionTiles;

        [Title("2D Transition Tiles")]
        [InfoBox("Additional transition tiles for 2D biome boundaries")]
        [FoldoutGroup("Transition")]
        [SerializeField]
        [Tooltip("Transparent tiles that point up (↑)")]
        private TileBase[] topTransitionTiles;

        [FoldoutGroup("Transition")]
        [SerializeField]
        [Tooltip("Transparent tiles that point down (↓)")]
        private TileBase[] bottomTransitionTiles;

        [Title("Outer Corners (Convex)")]
        [FoldoutGroup("Transition")]
        [SerializeField]
        [Tooltip("Outer corner transition tiles (NE corner - convex)")]
        private TileBase[] northEastCornerTiles;

        [FoldoutGroup("Transition")]
        [SerializeField]
        [Tooltip("Outer corner transition tiles (NW corner - convex)")]
        private TileBase[] northWestCornerTiles;

        [FoldoutGroup("Transition")]
        [SerializeField]
        [Tooltip("Outer corner transition tiles (SE corner - convex)")]
        private TileBase[] southEastCornerTiles;

        [FoldoutGroup("Transition")]
        [SerializeField]
        [Tooltip("Outer corner transition tiles (SW corner - convex)")]
        private TileBase[] southWestCornerTiles;
        
        [Title("Inner Corners (Concave)")]
        [FoldoutGroup("Transition")]
        [SerializeField]
        [Tooltip("Inner corner transition tiles (NE corner - concave)")]
        private TileBase[] innerNorthEastCornerTiles;

        [FoldoutGroup("Transition")]
        [SerializeField]
        [Tooltip("Inner corner transition tiles (NW corner - concave)")]
        private TileBase[] innerNorthWestCornerTiles;

        [FoldoutGroup("Transition")]
        [SerializeField]
        [Tooltip("Inner corner transition tiles (SE corner - concave)")]
        private TileBase[] innerSouthEastCornerTiles;

        [FoldoutGroup("Transition")]
        [SerializeField]
        [Tooltip("Inner corner transition tiles (SW corner - concave)")]
        private TileBase[] innerSouthWestCornerTiles;

        [FoldoutGroup("Transition")]
        [SerializeField, Range(0f, 1f)]
        [Tooltip("Chance to place transition tile at biome boundary")]
        private float transitionTileChance = 1.0f;

        [Title("Generation Settings")]
        [FoldoutGroup("Generation")]
        [SerializeField, Range(0.001f, 0.2f)]
        [Tooltip("Scale of the noise pattern")]
        private float noiseScale = 0.05f;

        [FoldoutGroup("Generation")]
        [SerializeField, Range(1, 8)]
        [Tooltip("Number of noise octaves for detail")]
        private int octaves = 4;

        [FoldoutGroup("Generation")]
        [SerializeField, Range(0f, 1f)]
        [Tooltip("How much each octave contributes")]
        private float persistence = 0.5f;

        [FoldoutGroup("Generation")]
        [SerializeField, Range(1f, 4f)]
        [Tooltip("How much detail is added with each octave")]
        private float lacunarity = 2f;

        [FoldoutGroup("Generation")]
        [SerializeField]
        [Tooltip("Seed offset for this biome's noise")]
        private Vector2 noiseOffset;

        [Title("Vegetation System")]
        [FoldoutGroup("Vegetation")]
        [SerializeField]
        [Tooltip("High grass vegetation sprites")]
        private Sprite[] highGrassVegetationSprites;

        [FoldoutGroup("Vegetation")]
        [SerializeField]
        [Tooltip("Low grass vegetation sprites")]
        private Sprite[] lowGrassVegetationSprites;

        [FoldoutGroup("Vegetation")]
        [SerializeField, Range(0f, 1f)]
        [Tooltip("Base chance to place vegetation")]
        private float vegetationDensity = 0.3f;

        [FoldoutGroup("Vegetation")]
        [SerializeField, Range(0f, 1f)]
        [Tooltip("Ratio of high grass to low grass (0 = all low, 1 = all high)")]
        private float highGrassRatio = 0.4f;

        [FoldoutGroup("Vegetation")]
        [SerializeField]
        [Tooltip("Use noise-based clustering for vegetation")]
        private bool useVegetationClustering = true;

        [FoldoutGroup("Vegetation")]
        [SerializeField, Range(0.01f, 0.5f)]
        [Tooltip("Scale of vegetation clustering noise")]
        private float vegetationNoiseScale = 0.1f;

        [FoldoutGroup("Vegetation")]
        [SerializeField]
        [Tooltip("Vegetation noise offset for variety")]
        private Vector2 vegetationNoiseOffset;

        [FoldoutGroup("Vegetation")]
        [SerializeField, Range(0f, 1f)]
        [Tooltip("Minimum noise value to place vegetation (creates clusters)")]
        private float vegetationNoiseThreshold = 0.4f;

        [FoldoutGroup("Vegetation")]
        [SerializeField, Range(0.1f, 2f)]
        [Tooltip("Global density multiplier for vegetation")]
        private float vegetationDensityMultiplier = 1f;
        
        [FoldoutGroup("Vegetation")]
        [SerializeField, Range(0.5f, 2f)]
        [Tooltip("Minimum scale for vegetation")]
        private float vegetationMinScale = 0.8f;
        
        [FoldoutGroup("Vegetation")]
        [SerializeField, Range(0.5f, 2f)]
        [Tooltip("Maximum scale for vegetation")]
        private float vegetationMaxScale = 1.2f;
        
        [FoldoutGroup("Vegetation")]
        [SerializeField, Range(0f, 1f)]
        [Tooltip("Chance to flip vegetation sprites horizontally")]
        private float vegetationFlipChance = 0.5f;
        
        [FoldoutGroup("Vegetation")]
        [SerializeField, Range(0f, 45f)]
        [Tooltip("Maximum random rotation for vegetation (in degrees)")]
        private float vegetationMaxRotation = 5f;
        
        [FoldoutGroup("Vegetation")]
        [SerializeField]
        [Tooltip("Y-axis offset for sorting (bottom of sprite)")]
        private float vegetationYSortingOffset = -0.5f;


        public string BiomeName => biomeName;
        public Color BiomeColor => biomeColor;
        public TileBase PrimaryGroundTile => primaryGroundTile;
        public TileBase[] SecondaryGroundTiles => secondaryGroundTiles;
        public TileBase[] DecorationTiles => decorationTiles;
        public float SecondaryTileChance => secondaryTileChance;
        public bool UseNoiseBasedTileSelection => useNoiseBasedTileSelection;
        public float DecorationChance => decorationChance;
        public TileBase[] LeftTransitionTiles => leftTransitionTiles;
        public TileBase[] RightTransitionTiles => rightTransitionTiles;
        public TileBase[] TopTransitionTiles => topTransitionTiles;
        public TileBase[] BottomTransitionTiles => bottomTransitionTiles;
        public TileBase[] NorthEastCornerTiles => northEastCornerTiles;
        public TileBase[] NorthWestCornerTiles => northWestCornerTiles;
        public TileBase[] SouthEastCornerTiles => southEastCornerTiles;
        public TileBase[] SouthWestCornerTiles => southWestCornerTiles;
        public float TransitionTileChance => transitionTileChance;
        public float NoiseScale => noiseScale;
        public int Octaves => octaves;
        public float Persistence => persistence;
        public float Lacunarity => lacunarity;
        public Vector2 NoiseOffset => noiseOffset;
        public Sprite[] HighGrassVegetationSprites => highGrassVegetationSprites;
        public Sprite[] LowGrassVegetationSprites => lowGrassVegetationSprites;
        public float VegetationDensity => vegetationDensity;
        public float HighGrassRatio => highGrassRatio;
        public bool UseVegetationClustering => useVegetationClustering;
        public float VegetationNoiseScale => vegetationNoiseScale;
        public Vector2 VegetationNoiseOffset => vegetationNoiseOffset;
        public float VegetationNoiseThreshold => vegetationNoiseThreshold;
        public float VegetationDensityMultiplier => vegetationDensityMultiplier;
        public float VegetationMinScale => vegetationMinScale;
        public float VegetationMaxScale => vegetationMaxScale;
        public float VegetationFlipChance => vegetationFlipChance;
        public float VegetationMaxRotation => vegetationMaxRotation;
        public float VegetationYSortingOffset => vegetationYSortingOffset;
        public bool UsePrefabVegetation => true; // Always use prefab system

        [Button("Randomize Noise Offset", ButtonSizes.Medium)]
        [FoldoutGroup("Generation")]
        private void RandomizeNoiseOffset()
        {
            noiseOffset = new Vector2(
                Random.Range(-10000f, 10000f),
                Random.Range(-10000f, 10000f)
            );
        }

        [Button("Randomize Vegetation Noise Offset", ButtonSizes.Medium)]
        [FoldoutGroup("Vegetation")]
        private void RandomizeVegetationNoiseOffset()
        {
            vegetationNoiseOffset = new Vector2(
                Random.Range(-10000f, 10000f),
                Random.Range(-10000f, 10000f)
            );
        }

        public TileBase GetRandomSecondaryTile()
        {
            if (secondaryGroundTiles == null || secondaryGroundTiles.Length == 0)
                return primaryGroundTile;

            return secondaryGroundTiles[Random.Range(0, secondaryGroundTiles.Length)];
        }

        public TileBase GetRandomDecorationTile()
        {
            if (decorationTiles == null || decorationTiles.Length == 0)
                return null;

            return decorationTiles[Random.Range(0, decorationTiles.Length)];
        }

        public TileBase GetTransitionTile(bool comingFromLeft)
        {
            TileBase[] tiles = comingFromLeft ? leftTransitionTiles : rightTransitionTiles;
            
            if (tiles == null || tiles.Length == 0)
                return null;

            // For boundary transitions, just use the first tile (no randomness for vertical consistency)
            return tiles[0];
        }

        public bool HasTransitionTiles(bool comingFromLeft)
        {
            TileBase[] tiles = comingFromLeft ? leftTransitionTiles : rightTransitionTiles;
            return tiles != null && tiles.Length > 0;
        }

        public TileBase GetTransitionTileForDirection(TransitionDirection direction, bool isInnerCorner = false)
        {
            TileBase[] tiles = null;
            
            // For single directions, use the appropriate tile set
            switch (direction)
            {
                case TransitionDirection.North:
                    tiles = topTransitionTiles;
                    break;
                case TransitionDirection.South:
                    tiles = bottomTransitionTiles;
                    break;
                case TransitionDirection.East:
                    tiles = rightTransitionTiles;
                    break;
                case TransitionDirection.West:
                    tiles = leftTransitionTiles;
                    break;
                case TransitionDirection.NorthEast:
                    tiles = isInnerCorner ? innerNorthEastCornerTiles : northEastCornerTiles;
                    break;
                case TransitionDirection.NorthWest:
                    tiles = isInnerCorner ? innerNorthWestCornerTiles : northWestCornerTiles;
                    break;
                case TransitionDirection.SouthEast:
                    tiles = isInnerCorner ? innerSouthEastCornerTiles : southEastCornerTiles;
                    break;
                case TransitionDirection.SouthWest:
                    tiles = isInnerCorner ? innerSouthWestCornerTiles : southWestCornerTiles;
                    break;
            }
            
            if (tiles == null || tiles.Length == 0)
                return null;
                
            // For consistency, use the first tile (no randomness at boundaries)
            return tiles[0];
        }

        public bool HasTransitionTilesForDirection(TransitionDirection direction, bool checkInnerCorner = false)
        {
            TileBase[] tiles = null;
            
            switch (direction)
            {
                case TransitionDirection.North:
                    tiles = topTransitionTiles;
                    break;
                case TransitionDirection.South:
                    tiles = bottomTransitionTiles;
                    break;
                case TransitionDirection.East:
                    tiles = rightTransitionTiles;
                    break;
                case TransitionDirection.West:
                    tiles = leftTransitionTiles;
                    break;
                case TransitionDirection.NorthEast:
                    tiles = checkInnerCorner ? innerNorthEastCornerTiles : northEastCornerTiles;
                    break;
                case TransitionDirection.NorthWest:
                    tiles = checkInnerCorner ? innerNorthWestCornerTiles : northWestCornerTiles;
                    break;
                case TransitionDirection.SouthEast:
                    tiles = checkInnerCorner ? innerSouthEastCornerTiles : southEastCornerTiles;
                    break;
                case TransitionDirection.SouthWest:
                    tiles = checkInnerCorner ? innerSouthWestCornerTiles : southWestCornerTiles;
                    break;
            }
            
            return tiles != null && tiles.Length > 0;
        }


        public Sprite GetRandomHighGrassVegetationSprite()
        {
            if (highGrassVegetationSprites == null || highGrassVegetationSprites.Length == 0)
                return null;

            return highGrassVegetationSprites[Random.Range(0, highGrassVegetationSprites.Length)];
        }

        public Sprite GetRandomLowGrassVegetationSprite()
        {
            if (lowGrassVegetationSprites == null || lowGrassVegetationSprites.Length == 0)
                return null;

            return lowGrassVegetationSprites[Random.Range(0, lowGrassVegetationSprites.Length)];
        }

        public Sprite GetRandomVegetationSprite(float highGrassChance)
        {
            bool useHighGrass = Random.value < highGrassChance;
            return useHighGrass ? GetRandomHighGrassVegetationSprite() : GetRandomLowGrassVegetationSprite();
        }

        public bool HasVegetation()
        {
            return (highGrassVegetationSprites != null && highGrassVegetationSprites.Length > 0) ||
                   (lowGrassVegetationSprites != null && lowGrassVegetationSprites.Length > 0);
        }

        #if UNITY_EDITOR
        [Button("Validate Biome Data", ButtonSizes.Large)]
        [PropertySpace(10)]
        private void ValidateBiomeData()
        {
            bool isValid = true;
            string validationMessage = "Validation Results:\n";

            if (primaryGroundTile == null)
            {
                validationMessage += "- ERROR: Primary ground tile is missing!\n";
                isValid = false;
            }
            else
            {
                validationMessage += "- Primary ground tile is set.\n";
            }

            if (secondaryGroundTiles == null || secondaryGroundTiles.Length == 0)
            {
                validationMessage += "- WARNING: No secondary ground tiles set.\n";
            }
            else
            {
                validationMessage += $"- {secondaryGroundTiles.Length} secondary ground tiles configured.\n";
            }

            if (decorationTiles == null || decorationTiles.Length == 0)
            {
                validationMessage += "- INFO: No decoration tiles set.\n";
            }
            else
            {
                validationMessage += $"- {decorationTiles.Length} decoration tiles configured.\n";
            }

            if (leftTransitionTiles == null || leftTransitionTiles.Length == 0)
            {
                validationMessage += "- INFO: No left transition tiles set.\n";
            }
            else
            {
                validationMessage += $"- {leftTransitionTiles.Length} left transition tiles configured.\n";
            }

            if (rightTransitionTiles == null || rightTransitionTiles.Length == 0)
            {
                validationMessage += "- INFO: No right transition tiles set.\n";
            }
            else
            {
                validationMessage += $"- {rightTransitionTiles.Length} right transition tiles configured.\n";
            }

            // Validate 2D transition tiles
            validationMessage += "\n2D Transition Tiles:\n";
            
            if (topTransitionTiles != null && topTransitionTiles.Length > 0)
                validationMessage += $"- {topTransitionTiles.Length} top transition tiles.\n";
            
            if (bottomTransitionTiles != null && bottomTransitionTiles.Length > 0)
                validationMessage += $"- {bottomTransitionTiles.Length} bottom transition tiles.\n";
            
            validationMessage += "\nOuter Corners (Convex):\n";
            if (northEastCornerTiles != null && northEastCornerTiles.Length > 0)
                validationMessage += $"- {northEastCornerTiles.Length} NE outer corner tiles.\n";
            
            if (northWestCornerTiles != null && northWestCornerTiles.Length > 0)
                validationMessage += $"- {northWestCornerTiles.Length} NW outer corner tiles.\n";
            
            if (southEastCornerTiles != null && southEastCornerTiles.Length > 0)
                validationMessage += $"- {southEastCornerTiles.Length} SE outer corner tiles.\n";
            
            if (southWestCornerTiles != null && southWestCornerTiles.Length > 0)
                validationMessage += $"- {southWestCornerTiles.Length} SW outer corner tiles.\n";
            
            validationMessage += "\nInner Corners (Concave):\n";
            if (innerNorthEastCornerTiles != null && innerNorthEastCornerTiles.Length > 0)
                validationMessage += $"- {innerNorthEastCornerTiles.Length} NE inner corner tiles.\n";
            
            if (innerNorthWestCornerTiles != null && innerNorthWestCornerTiles.Length > 0)
                validationMessage += $"- {innerNorthWestCornerTiles.Length} NW inner corner tiles.\n";
            
            if (innerSouthEastCornerTiles != null && innerSouthEastCornerTiles.Length > 0)
                validationMessage += $"- {innerSouthEastCornerTiles.Length} SE inner corner tiles.\n";
            
            if (innerSouthWestCornerTiles != null && innerSouthWestCornerTiles.Length > 0)
                validationMessage += $"- {innerSouthWestCornerTiles.Length} SW inner corner tiles.\n";

            // Validate vegetation system
            validationMessage += "\nVegetation System:\n";
            
            if (highGrassVegetationSprites != null && highGrassVegetationSprites.Length > 0)
                validationMessage += $"- {highGrassVegetationSprites.Length} high grass sprite types.\n";
            else
                validationMessage += "- INFO: No high grass sprites set.\n";
                
            if (lowGrassVegetationSprites != null && lowGrassVegetationSprites.Length > 0)
                validationMessage += $"- {lowGrassVegetationSprites.Length} low grass sprite types.\n";
            else
                validationMessage += "- INFO: No low grass sprites set.\n";
                
            if (HasVegetation())
            {
                validationMessage += $"- Vegetation density: {vegetationDensity:P0}\n";
                validationMessage += $"- High grass ratio: {highGrassRatio:P0}\n";
                validationMessage += $"- Clustering enabled: {useVegetationClustering}\n";
                
                if (useVegetationClustering)
                {
                    validationMessage += $"  - Noise scale: {vegetationNoiseScale}\n";
                    validationMessage += $"  - Noise threshold: {vegetationNoiseThreshold}\n";
                }
                
                validationMessage += $"- Vegetation density multiplier: {vegetationDensityMultiplier:F1}\n";
                validationMessage += $"- Scale range: {vegetationMinScale:F1} - {vegetationMaxScale:F1}\n";
                validationMessage += $"- Flip chance: {vegetationFlipChance:P0}\n";
                validationMessage += $"- Max rotation: {vegetationMaxRotation:F0}°\n";
                validationMessage += $"- Y sorting offset: {vegetationYSortingOffset:F2}\n";
            }

            validationMessage += isValid ? "\nBiome data is valid!" : "\nBiome data has errors!";
            
            if (isValid)
                Debug.Log($"<color=green>{validationMessage}</color>");
            else
                Debug.LogError(validationMessage);
        }
        #endif
    }
