/*
 * Sprite2DWindEnhanced Shader
 *
 * A comprehensive 2D sprite animation shader with two distinct movement systems:
 *
 * WIND SYSTEM: Realistic noise-based movement
 * - Uses noise textures for natural wind patterns
 * - Directional wind force with pulsing gusts
 * - Best for: Trees, large vegetation, flags, cloth
 *
 * GRASS SYSTEM: Stylized sine-wave movement
 * - Uses sine waves for smooth swaying motion
 * - Includes radial bending for natural curves
 * - Best for: Grass, small plants, hair, tentacles
 *
 * FEATURES:
 * - Height-based masking (rigid base, flexible top)
 * - Can use either system independently or combined
 * - Optimized with proper shader variants
 * - Compatible with Unity's Universal Render Pipeline
 */

Shader "Custom/Sprite2DWindEnhanced"
{
    Properties
    {
        // Base Sprite Properties
        _MainTex ("Sprite Texture", 2D) = "white" {}
        _Color ("Tint", Color) = (1,1,1,1)

        [Header(Effect Selection)]
        [Toggle(_ENABLE_WIND)] _EnableWindSystem ("Enable Wind System", Float) = 1
        [Tooltip("Enables realistic noise-based wind movement. Best for trees, large vegetation, flags.")]
        [Toggle(_ENABLE_GRASS)] _EnableGrassSystem ("Enable Grass System", Float) = 0
        [Tooltip("Enables stylized sine-wave grass movement. Best for grass, small plants, hair.")]

        [Header(Wind System - Realistic Noise-Based Movement)]
        [Tooltip("Overall intensity of wind displacement (0 = no movement, 1 = maximum movement)")]
        _WindIntensity ("Wind Intensity", Range(0, 1)) = 0.5
        [Tooltip("Speed of wind animation (higher = faster wind changes)")]
        _WindSpeed ("Wind Speed", Range(0, 10)) = 1.0
        [Tooltip("Direction of wind force (X, Y components define wind direction)")]
        _WindDirection ("Wind Direction", Vector) = (1, 0, 0, 0)
        [Tooltip("Scale of wind noise pattern (higher = more detailed/chaotic wind)")]
        _WindNoiseFrequency ("Wind Noise Frequency", Range(0.1, 10)) = 1.0
        [Tooltip("Frequency of wind intensity pulsing (creates wind gusts)")]
        _WindPulseFrequency ("Wind Pulse Frequency", Range(0, 10)) = 2.0
        [Tooltip("Strength of wind pulsing effect (0 = constant wind, 1 = strong gusts)")]
        _WindPulseAmplitude ("Wind Pulse Amplitude", Range(0, 1)) = 0.1
        [Tooltip("Noise texture used for wind randomness (use Perlin noise texture)")]
        _WindNoiseTex ("Wind Noise Texture", 2D) = "white" {}

        [Header(Grass System - Stylized Sine-Wave Movement)]
        [Tooltip("Speed of grass swaying animation (higher = faster swaying)")]
        _GrassSwaySpeed ("Grass Sway Speed", Range(0, 20)) = 10
        [Tooltip("Intensity of grass movement displacement (higher = more movement)")]
        _GrassSwayIntensity ("Grass Sway Intensity", Range(0, 2)) = 1.5
        [Tooltip("Amount of radial/circular bending effect (creates natural grass curves)")]
        _GrassRadialSway ("Grass Radial Sway", Range(0, 1)) = 0.2
        [Tooltip("Manual time offset for animation (useful for variation between objects)")]
        _GrassTimeOffset ("Grass Time Offset", Range(-5, 5)) = 0
        [Tooltip("Constant horizontal push/bias (negative = left, positive = right)")]
        _GrassHorizontalBias ("Grass Horizontal Bias", Range(-0.5, 0.5)) = 0
        [Tooltip("Constant vertical push/bias (negative = down, positive = up)")]
        _GrassVerticalBias ("Grass Vertical Bias", Range(-0.5, 0.5)) = 0

        [Header(Movement Masking - Affects Both Systems)]
        [Tooltip("Movement scale at sprite bottom/base (0 = rigid base, 1 = full movement)")]
        _BottomMovementScale ("Bottom Movement Scale", Range(0, 1)) = 0.0
        [Tooltip("Movement scale at sprite top (0 = no movement, 1 = full movement)")]
        _TopMovementScale ("Top Movement Scale", Range(0, 1)) = 1.0

        // Unity System Properties (Hidden)
        [HideInInspector] _RendererColor ("RendererColor", Color) = (1,1,1,1)
        [HideInInspector] _Flip ("Flip", Vector) = (1,1,1,1)
        [HideInInspector] _AlphaTex ("External Alpha", 2D) = "white" {}
        [HideInInspector] _EnableExternalAlpha ("Enable External Alpha", Float) = 0
    }

    SubShader
    {
        Tags
        {
            "Queue"="Transparent"
            "IgnoreProjector"="True"
            "RenderType"="Transparent"
            "PreviewType"="Plane"
            "CanUseSpriteAtlas"="True"
        }

        Cull Off
        Lighting Off
        ZWrite Off
        Blend One OneMinusSrcAlpha

        Pass
        {
            Name "Sprite2DWindEnhancedUnlit"
            Tags { "LightMode" = "Universal2D" }

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile _ PIXELSNAP_ON
            #pragma multi_compile _ CLIPPING_ON
            #pragma shader_feature_local _ENABLE_WIND
            #pragma shader_feature_local _ENABLE_GRASS
            
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

            CBUFFER_START(UnityPerMaterial)
                float4 _MainTex_ST;
                half4 _Color;
                half4 _RendererColor;
                // Wind System Properties
                float4 _WindDirection;
                float _WindIntensity;
                float _WindSpeed;
                float _WindNoiseFrequency;
                float _WindPulseFrequency;
                float _WindPulseAmplitude;
                // Grass System Properties
                float _GrassSwaySpeed;
                float _GrassSwayIntensity;
                float _GrassRadialSway;
                float _GrassTimeOffset;
                float _GrassHorizontalBias;
                float _GrassVerticalBias;
                // Movement Masking Properties
                float _BottomMovementScale;
                float _TopMovementScale;
            CBUFFER_END

            struct appdata_t
            {
                float4 vertex   : POSITION;
                float4 color    : COLOR;
                float2 texcoord : TEXCOORD0;
            };

            struct v2f
            {
                float4 vertex   : SV_POSITION;
                half4 color    : COLOR;
                float2 texcoord  : TEXCOORD0;
                float4 worldPos : TEXCOORD1;
            };

            TEXTURE2D(_MainTex);
            SAMPLER(sampler_MainTex);
            TEXTURE2D(_AlphaTex);
            SAMPLER(sampler_AlphaTex);
            TEXTURE2D(_WindNoiseTex);
            SAMPLER(sampler_WindNoiseTex);

            v2f vert(appdata_t IN)
            {
                v2f OUT;
                
                float4 worldPos = mul(unity_ObjectToWorld, IN.vertex);
                OUT.worldPos = worldPos;
                
                // === HEIGHT-BASED MASKING ===
                // Calculate movement influence based on sprite height (Y coordinate)
                // Bottom of sprite (Y=0) uses _BottomMovementScale, top (Y=1) uses _TopMovementScale
                float heightFactor = saturate(IN.texcoord.y);
                heightFactor = lerp(_BottomMovementScale, _TopMovementScale, heightFactor);

                float time = _Time.y;
                float2 totalOffset = float2(0, 0);

                #ifdef _ENABLE_WIND
                    // === WIND SYSTEM: Realistic Noise-Based Movement ===
                    // Creates natural wind movement using noise texture and directional force
                    float adjustedTime = time * _WindSpeed;

                    // Sample noise texture with scrolling UV coordinates for animated wind
                    float2 noiseUV = worldPos.xz * _WindNoiseFrequency * 0.1 + float2(adjustedTime * 0.05, adjustedTime * 0.02);
                    float4 noiseSample = SAMPLE_TEXTURE2D_LOD(_WindNoiseTex, sampler_WindNoiseTex, noiseUV, 0);

                    // Convert noise from [0,1] to [-1,1] range and combine R and G channels
                    float noise1 = noiseSample.r * 2.0 - 1.0;
                    float noise2 = noiseSample.g * 2.0 - 1.0;
                    float combinedNoise = (noise1 + noise2) * 0.5;

                    // Create pulsing wind gusts using sine wave
                    float pulseWave = sin(adjustedTime * _WindPulseFrequency) * _WindPulseAmplitude + (1.0 - _WindPulseAmplitude);

                    // Apply wind direction, intensity, height masking, noise, and pulsing
                    float2 windDirNorm = normalize(_WindDirection.xy);
                    float2 windOffset = windDirNorm * _WindIntensity * heightFactor * combinedNoise * pulseWave;
                    totalOffset += windOffset;
                #endif
                
                #ifdef _ENABLE_GRASS
                    // === GRASS SYSTEM: Stylized Sine-Wave Movement ===
                    // Creates stylized grass swaying using sine waves and radial bending
                    float grassTime = time * _GrassSwaySpeed * 0.1 + _GrassTimeOffset;

                    // Add world position variation for more natural per-object movement
                    float posVariation = sin(worldPos.x * 0.5 + worldPos.z * 0.3) * 0.5 + 0.5;

                    // Primary swaying motion with horizontal bias
                    float swayOffset = sin(grassTime + posVariation * 3.14159) + _GrassHorizontalBias;

                    // Basic horizontal and vertical displacement
                    float2 grassOffset = float2(
                        swayOffset * _GrassSwayIntensity * 0.01,
                        _GrassVerticalBias * _GrassSwayIntensity * 0.01
                    );
                    grassOffset *= heightFactor;

                    // Radial bending effect - creates natural curved grass movement
                    float2 centerOffset = IN.texcoord - float2(0.5, 0);
                    float distFromCenter = length(centerOffset);
                    float2 radialDir = normalize(centerOffset + float2(0.001, 0.001)); // Avoid divide by zero
                    float2 radialOffset = radialDir * sin(grassTime * 1.5) * _GrassRadialSway * distFromCenter * heightFactor;

                    totalOffset += grassOffset + radialOffset;
                #endif
                
                IN.vertex.xy += totalOffset;
                
                OUT.vertex = TransformObjectToHClip(IN.vertex.xyz);
                OUT.texcoord = IN.texcoord;
                OUT.color = IN.color * _Color * _RendererColor;

                #ifdef PIXELSNAP_ON
                OUT.vertex = UnityPixelSnap(OUT.vertex);
                #endif

                return OUT;
            }

            half4 frag(v2f IN) : SV_Target
            {
                half4 texColor = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, IN.texcoord);
                half4 color = texColor * IN.color;
                color.rgb *= color.a;
                
                #ifdef CLIPPING_ON
                clip(color.a - 0.001);
                #endif

                return color;
            }
            ENDHLSL
        }
        
        Pass
        {
            Name "Sprite2DWindEnhancedLit"
            Tags { "LightMode" = "UniversalForward" }

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile _ PIXELSNAP_ON
            #pragma multi_compile _ CLIPPING_ON
            #pragma shader_feature_local _ENABLE_WIND
            #pragma shader_feature_local _ENABLE_GRASS
            
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"

            CBUFFER_START(UnityPerMaterial)
                float4 _MainTex_ST;
                half4 _Color;
                half4 _RendererColor;
                // Wind System Properties
                float4 _WindDirection;
                float _WindIntensity;
                float _WindSpeed;
                float _WindNoiseFrequency;
                float _WindPulseFrequency;
                float _WindPulseAmplitude;
                // Grass System Properties
                float _GrassSwaySpeed;
                float _GrassSwayIntensity;
                float _GrassRadialSway;
                float _GrassTimeOffset;
                float _GrassHorizontalBias;
                float _GrassVerticalBias;
                // Movement Masking Properties
                float _BottomMovementScale;
                float _TopMovementScale;
            CBUFFER_END

            struct appdata_t
            {
                float4 vertex   : POSITION;
                float4 color    : COLOR;
                float2 texcoord : TEXCOORD0;
                float3 normal   : NORMAL;
            };

            struct v2f
            {
                float4 vertex   : SV_POSITION;
                half4 color    : COLOR;
                float2 texcoord  : TEXCOORD0;
                float3 worldPos : TEXCOORD1;
                float3 worldNormal : TEXCOORD2;
            };

            TEXTURE2D(_MainTex);
            SAMPLER(sampler_MainTex);
            TEXTURE2D(_AlphaTex);
            SAMPLER(sampler_AlphaTex);
            TEXTURE2D(_WindNoiseTex);
            SAMPLER(sampler_WindNoiseTex);

            v2f vert(appdata_t IN)
            {
                v2f OUT;
                
                float4 worldPos = mul(unity_ObjectToWorld, IN.vertex);
                
                // === HEIGHT-BASED MASKING ===
                // Calculate movement influence based on sprite height (Y coordinate)
                // Bottom of sprite (Y=0) uses _BottomMovementScale, top (Y=1) uses _TopMovementScale
                float heightFactor = saturate(IN.texcoord.y);
                heightFactor = lerp(_BottomMovementScale, _TopMovementScale, heightFactor);

                float time = _Time.y;
                float2 totalOffset = float2(0, 0);

                #ifdef _ENABLE_WIND
                    // === WIND SYSTEM: Realistic Noise-Based Movement ===
                    // Creates natural wind movement using noise texture and directional force
                    float adjustedTime = time * _WindSpeed;

                    // Sample noise texture with scrolling UV coordinates for animated wind
                    float2 noiseUV = worldPos.xz * _WindNoiseFrequency * 0.1 + float2(adjustedTime * 0.05, adjustedTime * 0.02);
                    float4 noiseSample = SAMPLE_TEXTURE2D_LOD(_WindNoiseTex, sampler_WindNoiseTex, noiseUV, 0);

                    // Convert noise from [0,1] to [-1,1] range and combine R and G channels
                    float noise1 = noiseSample.r * 2.0 - 1.0;
                    float noise2 = noiseSample.g * 2.0 - 1.0;
                    float combinedNoise = (noise1 + noise2) * 0.5;

                    // Create pulsing wind gusts using sine wave
                    float pulseWave = sin(adjustedTime * _WindPulseFrequency) * _WindPulseAmplitude + (1.0 - _WindPulseAmplitude);

                    // Apply wind direction, intensity, height masking, noise, and pulsing
                    float2 windDirNorm = normalize(_WindDirection.xy);
                    float2 windOffset = windDirNorm * _WindIntensity * heightFactor * combinedNoise * pulseWave;
                    totalOffset += windOffset;
                #endif
                
                #ifdef _ENABLE_GRASS
                    // === GRASS SYSTEM: Stylized Sine-Wave Movement ===
                    // Creates stylized grass swaying using sine waves and radial bending
                    float grassTime = time * _GrassSwaySpeed * 0.1 + _GrassTimeOffset;

                    // Add world position variation for more natural per-object movement
                    float posVariation = sin(worldPos.x * 0.5 + worldPos.z * 0.3) * 0.5 + 0.5;

                    // Primary swaying motion with horizontal bias
                    float swayOffset = sin(grassTime + posVariation * 3.14159) + _GrassHorizontalBias;

                    // Basic horizontal and vertical displacement
                    float2 grassOffset = float2(
                        swayOffset * _GrassSwayIntensity * 0.01,
                        _GrassVerticalBias * _GrassSwayIntensity * 0.01
                    );
                    grassOffset *= heightFactor;

                    // Radial bending effect - creates natural curved grass movement
                    float2 centerOffset = IN.texcoord - float2(0.5, 0);
                    float distFromCenter = length(centerOffset);
                    float2 radialDir = normalize(centerOffset + float2(0.001, 0.001)); // Avoid divide by zero
                    float2 radialOffset = radialDir * sin(grassTime * 1.5) * _GrassRadialSway * distFromCenter * heightFactor;

                    totalOffset += grassOffset + radialOffset;
                #endif
                
                IN.vertex.xy += totalOffset;
                
                OUT.vertex = TransformObjectToHClip(IN.vertex.xyz);
                OUT.texcoord = IN.texcoord;
                OUT.color = IN.color * _Color * _RendererColor;
                OUT.worldPos = mul(unity_ObjectToWorld, IN.vertex).xyz;
                OUT.worldNormal = TransformObjectToWorldNormal(IN.normal);

                #ifdef PIXELSNAP_ON
                OUT.vertex = UnityPixelSnap(OUT.vertex);
                #endif

                return OUT;
            }

            half4 frag(v2f IN) : SV_Target
            {
                half4 texColor = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, IN.texcoord);
                half4 color = texColor * IN.color;
                
                // Simple lighting
                Light mainLight = GetMainLight();
                float3 lighting = dot(IN.worldNormal, mainLight.direction) * 0.5 + 0.5;
                color.rgb *= lighting * mainLight.color;
                
                color.rgb *= color.a;
                
                #ifdef CLIPPING_ON
                clip(color.a - 0.001);
                #endif

                return color;
            }
            ENDHLSL
        }
    }
    
    FallBack "Sprites/Default"
}