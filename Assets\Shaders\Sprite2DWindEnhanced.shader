Shader "Custom/Sprite2DWindEnhanced"
{
    Properties
    {
        _MainTex ("Sprite Texture", 2D) = "white" {}
        _Color ("Tint", Color) = (1,1,1,1)
        
        [Header(Wind Effect Controls)]
        [Toggle(_ENABLE_WIND)] _EnableWind ("Enable Wind Effect", Float) = 1
        [Toggle(_ENABLE_GRASS)] _EnableGrass ("Enable Grass Effect", Float) = 0
        
        [Header(Original Wind Effect)]
        _WindStrength ("Wind Strength", Range(0, 1)) = 0.5
        _WindSpeed ("Wind Speed", Range(0, 10)) = 1.0
        _WindDirection ("Wind Direction", Vector) = (1, 0, 0, 0)
        _WindNoiseScale ("Wind Noise Scale", Range(0.1, 10)) = 1.0
        _WindPulseFrequency ("Wind Pulse Frequency", Range(0, 10)) = 2.0
        _WindPulseAmplitude ("Wind Pulse Amplitude", Range(0, 1)) = 0.1
        _WindNoiseTex ("Wind Noise Texture", 2D) = "white" {}
        
        [Header(Grass Movement Effect)]
        _GrassSpeed ("Grass Speed", Range(0, 50)) = 10
        _GrassWindAmount ("Grass Wind Amount", Range(0, 50)) = 15
        _GrassRadialBend ("Grass Radial Bend", Range(0, 5)) = 0.2
        _GrassManualAnimate ("Grass Manual Time", Range(-10, 10)) = 0
        _GrassXPush ("Grass X Push", Range(-1, 1)) = 0
        _GrassYPush ("Grass Y Push", Range(-1, 1)) = 0
        
        [Header(Height Masking)]
        _BaseInfluence ("Base Wind Influence", Range(0, 1)) = 0.0
        _TopInfluence ("Top Wind Influence", Range(0, 1)) = 1.0
        
        [HideInInspector] _RendererColor ("RendererColor", Color) = (1,1,1,1)
        [HideInInspector] _Flip ("Flip", Vector) = (1,1,1,1)
        [HideInInspector] _AlphaTex ("External Alpha", 2D) = "white" {}
        [HideInInspector] _EnableExternalAlpha ("Enable External Alpha", Float) = 0
    }

    SubShader
    {
        Tags
        {
            "Queue"="Transparent"
            "IgnoreProjector"="True"
            "RenderType"="Transparent"
            "PreviewType"="Plane"
            "CanUseSpriteAtlas"="True"
        }

        Cull Off
        Lighting Off
        ZWrite Off
        Blend One OneMinusSrcAlpha

        Pass
        {
            Name "Sprite2DWindEnhancedUnlit"
            Tags { "LightMode" = "Universal2D" }

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile _ PIXELSNAP_ON
            #pragma multi_compile _ CLIPPING_ON
            #pragma shader_feature_local _ENABLE_WIND
            #pragma shader_feature_local _ENABLE_GRASS
            
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

            CBUFFER_START(UnityPerMaterial)
                float4 _MainTex_ST;
                half4 _Color;
                half4 _RendererColor;
                float4 _WindDirection;
                float _WindStrength;
                float _WindSpeed;
                float _WindNoiseScale;
                float _WindPulseFrequency;
                float _WindPulseAmplitude;
                float _BaseInfluence;
                float _TopInfluence;
                float _GrassSpeed;
                float _GrassWindAmount;
                float _GrassRadialBend;
                float _GrassManualAnimate;
                float _GrassXPush;
                float _GrassYPush;
            CBUFFER_END

            struct appdata_t
            {
                float4 vertex   : POSITION;
                float4 color    : COLOR;
                float2 texcoord : TEXCOORD0;
            };

            struct v2f
            {
                float4 vertex   : SV_POSITION;
                half4 color    : COLOR;
                float2 texcoord  : TEXCOORD0;
                float4 worldPos : TEXCOORD1;
            };

            TEXTURE2D(_MainTex);
            SAMPLER(sampler_MainTex);
            TEXTURE2D(_AlphaTex);
            SAMPLER(sampler_AlphaTex);
            TEXTURE2D(_WindNoiseTex);
            SAMPLER(sampler_WindNoiseTex);

            v2f vert(appdata_t IN)
            {
                v2f OUT;
                
                float4 worldPos = mul(unity_ObjectToWorld, IN.vertex);
                OUT.worldPos = worldPos;
                
                // Height-based masking for both effects
                float heightFactor = saturate(IN.texcoord.y);
                heightFactor = lerp(_BaseInfluence, _TopInfluence, heightFactor);
                
                float time = _Time.y;
                float2 totalOffset = float2(0, 0);
                
                #ifdef _ENABLE_WIND
                    // Original wind effect
                    float adjustedTime = time * _WindSpeed;
                    float2 noiseUV = worldPos.xz * _WindNoiseScale * 0.1 + float2(adjustedTime * 0.05, adjustedTime * 0.02);
                    float4 noiseSample = SAMPLE_TEXTURE2D_LOD(_WindNoiseTex, sampler_WindNoiseTex, noiseUV, 0);
                    float noise1 = noiseSample.r * 2.0 - 1.0;
                    float noise2 = noiseSample.g * 2.0 - 1.0;
                    float combinedNoise = (noise1 + noise2) * 0.5;
                    
                    float pulseWave = sin(adjustedTime * _WindPulseFrequency) * _WindPulseAmplitude + (1.0 - _WindPulseAmplitude);
                    float2 windDirNorm = normalize(_WindDirection.xy);
                    float2 windOffset = windDirNorm * _WindStrength * heightFactor * combinedNoise * pulseWave;
                    totalOffset += windOffset;
                #endif
                
                #ifdef _ENABLE_GRASS
                    // Grass movement effect
                    float grassTime = time * _GrassSpeed * 0.1 + _GrassManualAnimate;
                    
                    // Add world position variation for more natural movement
                    float posVariation = sin(worldPos.x * 0.5 + worldPos.z * 0.3) * 0.5 + 0.5;
                    float windOffset = sin(grassTime + posVariation * 3.14159) + _GrassXPush;
                    
                    // Basic horizontal displacement
                    float2 grassOffset = float2(windOffset * _GrassWindAmount * 0.01, _GrassYPush * _GrassWindAmount * 0.01);
                    grassOffset *= heightFactor;
                    
                    // Radial bending effect
                    float2 centerOffset = IN.texcoord - float2(0.5, 0);
                    float distFromCenter = length(centerOffset);
                    float2 radialDir = normalize(centerOffset + float2(0.001, 0.001)); // Avoid divide by zero
                    float2 radialOffset = radialDir * sin(grassTime * 1.5) * _GrassRadialBend * distFromCenter * heightFactor;
                    
                    totalOffset += grassOffset + radialOffset;
                #endif
                
                IN.vertex.xy += totalOffset;
                
                OUT.vertex = TransformObjectToHClip(IN.vertex.xyz);
                OUT.texcoord = IN.texcoord;
                OUT.color = IN.color * _Color * _RendererColor;

                #ifdef PIXELSNAP_ON
                OUT.vertex = UnityPixelSnap(OUT.vertex);
                #endif

                return OUT;
            }

            half4 frag(v2f IN) : SV_Target
            {
                half4 texColor = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, IN.texcoord);
                half4 color = texColor * IN.color;
                color.rgb *= color.a;
                
                #ifdef CLIPPING_ON
                clip(color.a - 0.001);
                #endif

                return color;
            }
            ENDHLSL
        }
        
        Pass
        {
            Name "Sprite2DWindEnhancedLit"
            Tags { "LightMode" = "UniversalForward" }

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile _ PIXELSNAP_ON
            #pragma multi_compile _ CLIPPING_ON
            #pragma shader_feature_local _ENABLE_WIND
            #pragma shader_feature_local _ENABLE_GRASS
            
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"

            CBUFFER_START(UnityPerMaterial)
                float4 _MainTex_ST;
                half4 _Color;
                half4 _RendererColor;
                float4 _WindDirection;
                float _WindStrength;
                float _WindSpeed;
                float _WindNoiseScale;
                float _WindPulseFrequency;
                float _WindPulseAmplitude;
                float _BaseInfluence;
                float _TopInfluence;
                float _GrassSpeed;
                float _GrassWindAmount;
                float _GrassRadialBend;
                float _GrassManualAnimate;
                float _GrassXPush;
                float _GrassYPush;
            CBUFFER_END

            struct appdata_t
            {
                float4 vertex   : POSITION;
                float4 color    : COLOR;
                float2 texcoord : TEXCOORD0;
                float3 normal   : NORMAL;
            };

            struct v2f
            {
                float4 vertex   : SV_POSITION;
                half4 color    : COLOR;
                float2 texcoord  : TEXCOORD0;
                float3 worldPos : TEXCOORD1;
                float3 worldNormal : TEXCOORD2;
            };

            TEXTURE2D(_MainTex);
            SAMPLER(sampler_MainTex);
            TEXTURE2D(_AlphaTex);
            SAMPLER(sampler_AlphaTex);
            TEXTURE2D(_WindNoiseTex);
            SAMPLER(sampler_WindNoiseTex);

            v2f vert(appdata_t IN)
            {
                v2f OUT;
                
                float4 worldPos = mul(unity_ObjectToWorld, IN.vertex);
                
                // Height-based masking for both effects
                float heightFactor = saturate(IN.texcoord.y);
                heightFactor = lerp(_BaseInfluence, _TopInfluence, heightFactor);
                
                float time = _Time.y;
                float2 totalOffset = float2(0, 0);
                
                #ifdef _ENABLE_WIND
                    // Original wind effect
                    float adjustedTime = time * _WindSpeed;
                    float2 noiseUV = worldPos.xz * _WindNoiseScale * 0.1 + float2(adjustedTime * 0.05, adjustedTime * 0.02);
                    float4 noiseSample = SAMPLE_TEXTURE2D_LOD(_WindNoiseTex, sampler_WindNoiseTex, noiseUV, 0);
                    float noise1 = noiseSample.r * 2.0 - 1.0;
                    float noise2 = noiseSample.g * 2.0 - 1.0;
                    float combinedNoise = (noise1 + noise2) * 0.5;
                    
                    float pulseWave = sin(adjustedTime * _WindPulseFrequency) * _WindPulseAmplitude + (1.0 - _WindPulseAmplitude);
                    float2 windDirNorm = normalize(_WindDirection.xy);
                    float2 windOffset = windDirNorm * _WindStrength * heightFactor * combinedNoise * pulseWave;
                    totalOffset += windOffset;
                #endif
                
                #ifdef _ENABLE_GRASS
                    // Grass movement effect
                    float grassTime = time * _GrassSpeed * 0.1 + _GrassManualAnimate;
                    
                    // Add world position variation for more natural movement
                    float posVariation = sin(worldPos.x * 0.5 + worldPos.z * 0.3) * 0.5 + 0.5;
                    float windOffset = sin(grassTime + posVariation * 3.14159) + _GrassXPush;
                    
                    // Basic horizontal displacement
                    float2 grassOffset = float2(windOffset * _GrassWindAmount * 0.01, _GrassYPush * _GrassWindAmount * 0.01);
                    grassOffset *= heightFactor;
                    
                    // Radial bending effect
                    float2 centerOffset = IN.texcoord - float2(0.5, 0);
                    float distFromCenter = length(centerOffset);
                    float2 radialDir = normalize(centerOffset + float2(0.001, 0.001)); // Avoid divide by zero
                    float2 radialOffset = radialDir * sin(grassTime * 1.5) * _GrassRadialBend * distFromCenter * heightFactor;
                    
                    totalOffset += grassOffset + radialOffset;
                #endif
                
                IN.vertex.xy += totalOffset;
                
                OUT.vertex = TransformObjectToHClip(IN.vertex.xyz);
                OUT.texcoord = IN.texcoord;
                OUT.color = IN.color * _Color * _RendererColor;
                OUT.worldPos = mul(unity_ObjectToWorld, IN.vertex).xyz;
                OUT.worldNormal = TransformObjectToWorldNormal(IN.normal);

                #ifdef PIXELSNAP_ON
                OUT.vertex = UnityPixelSnap(OUT.vertex);
                #endif

                return OUT;
            }

            half4 frag(v2f IN) : SV_Target
            {
                half4 texColor = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, IN.texcoord);
                half4 color = texColor * IN.color;
                
                // Simple lighting
                Light mainLight = GetMainLight();
                float3 lighting = dot(IN.worldNormal, mainLight.direction) * 0.5 + 0.5;
                color.rgb *= lighting * mainLight.color;
                
                color.rgb *= color.a;
                
                #ifdef CLIPPING_ON
                clip(color.a - 0.001);
                #endif

                return color;
            }
            ENDHLSL
        }
    }
    
    FallBack "Sprites/Default"
}