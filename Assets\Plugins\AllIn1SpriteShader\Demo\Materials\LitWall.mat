%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: LitWall
  m_Shader: {fileID: 4800000, guid: 86ce7e600deb17e429b8be445bb652f7, type: 3}
  m_ShaderKeywords: 
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ColorRampTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ColorRampTexGradient:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ColorSwapTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DistortTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FadeBurnTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FadeTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GlowTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: c6ad7b22efb776c448982ca631b16f73, type: 3}
        m_Scale: {x: 5, y: 5}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 2800000, guid: fdff8a56ae2c830478b84c6807aa0408, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineDistortTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OutlineTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OverlayTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShineMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Floats:
    - _Alpha: 1
    - _AlphaCutoffValue: 0.25
    - _AlphaOutlineBlend: 1
    - _AlphaOutlineGlow: 5
    - _AlphaOutlineMinAlpha: 0
    - _AlphaOutlinePower: 1
    - _AlphaRoundThreshold: 0.5
    - _BillboardY: 0
    - _BlurHD: 0
    - _BlurIntensity: 10
    - _Brightness: 0
    - _BumpScale: 1
    - _ChromAberrAlpha: 0.4
    - _ChromAberrAmount: 1
    - _ClipUvDown: 0
    - _ClipUvLeft: 0
    - _ClipUvRight: 0
    - _ClipUvUp: 0
    - _ColorChangeLuminosity: 0
    - _ColorChangeTolerance: 0.25
    - _ColorChangeTolerance2: 0.25
    - _ColorChangeTolerance3: 0.25
    - _ColorRampBlend: 1
    - _ColorRampLuminosity: 0
    - _ColorRampOutline: 0
    - _ColorSwapBlend: 1
    - _ColorSwapBlueLuminosity: 0.5
    - _ColorSwapGreenLuminosity: 0.5
    - _ColorSwapRedLuminosity: 0.5
    - _Contrast: 1
    - _CullingOption: 0
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DistortAmount: 0.5
    - _DistortTexXSpeed: 5
    - _DistortTexYSpeed: 5
    - _DstBlend: 0
    - _EditorDrawers: 6
    - _FadeAmount: -0.1
    - _FadeBurnGlow: 2
    - _FadeBurnTransition: 0.075
    - _FadeBurnWidth: 0.025
    - _FishEyeUvAmount: 0.35
    - _FlickerAlpha: 0
    - _FlickerFreq: 0.2
    - _FlickerPercent: 0.05
    - _GhostBlend: 1
    - _GhostColorBoost: 1
    - _GhostTransparency: 0
    - _GlitchAmount: 3
    - _GlitchSize: 1
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _Glow: 10
    - _GlowGlobal: 1
    - _GradBlend: 1
    - _GradBoostX: 1.2
    - _GradBoostY: 1.2
    - _GradIsRadial: 0
    - _GrassManualAnim: 1
    - _GrassManualToggle: 0
    - _GrassRadialBend: 0.1
    - _GrassSpeed: 2
    - _GrassWind: 20
    - _GreyscaleBlend: 1
    - _GreyscaleLuminosity: 0
    - _GreyscaleOutline: 0
    - _HandDrawnAmount: 10
    - _HandDrawnSpeed: 5
    - _HitEffectBlend: 1
    - _HitEffectGlow: 5
    - _HologramBlend: 1
    - _HologramMaxAlpha: 0.75
    - _HologramMinAlpha: 0.1
    - _HologramStripesAmount: 0.1
    - _HologramStripesSpeed: 4.5
    - _HologramUnmodAmount: 0
    - _HsvBright: 1
    - _HsvSaturation: 1
    - _HsvShift: 180
    - _InnerOutlineAlpha: 1
    - _InnerOutlineGlow: 4
    - _InnerOutlineThickness: 1
    - _MaxXUV: 1
    - _MaxYUV: 1
    - _Metallic: 0
    - _MinXUV: 0
    - _MinYUV: 0
    - _Mode: 0
    - _MotionBlurAngle: 0.1
    - _MotionBlurDist: 1.25
    - _MyDstMode: 10
    - _MySrcMode: 5
    - _NegativeAmount: 1
    - _NormalStrength: 5
    - _OcclusionStrength: 1
    - _OffsetUvX: 0
    - _OffsetUvY: 0
    - _OnlyInnerOutline: 0
    - _OnlyOutline: 0
    - _OutlineAlpha: 1
    - _OutlineDistortAmount: 0.5
    - _OutlineDistortTexXSpeed: 5
    - _OutlineDistortTexYSpeed: 5
    - _OutlineGlow: 1.5
    - _OutlinePixelWidth: 1
    - _OutlineTexXSpeed: 10
    - _OutlineTexYSpeed: 0
    - _OutlineWidth: 0.004
    - _OverlayBlend: 1
    - _OverlayGlow: 1
    - _OverlayTextureScrollXSpeed: 0.25
    - _OverlayTextureScrollYSpeed: 0.25
    - _Parallax: 0.02
    - _PinchUvAmount: 0.35
    - _PixelateSize: 32
    - _PosterizeGamma: 0.75
    - _PosterizeNumColors: 8
    - _PosterizeOutline: 0
    - _RadialClip: 45
    - _RadialClip2: 0
    - _RadialStartAngle: 90
    - _RandomSeed: 0
    - _RectSize: 1
    - _RotateUvAmount: 0
    - _RoundWaveSpeed: 2
    - _RoundWaveStrength: 0.7
    - _ShadowAlpha: 0.5
    - _ShadowX: 0.1
    - _ShadowY: -0.05
    - _ShakeUvSpeed: 2.5
    - _ShakeUvX: 1.5
    - _ShakeUvY: 1
    - _ShineGlow: 1
    - _ShineLocation: 0.5
    - _ShineRotate: 0
    - _ShineWidth: 0.1
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _TextureScrollXSpeed: 1
    - _TextureScrollYSpeed: 0
    - _TwistUvAmount: 1
    - _TwistUvPosX: 0.5
    - _TwistUvPosY: 0.5
    - _TwistUvRadius: 0.75
    - _UVSec: 0
    - _WarpScale: 0.5
    - _WarpSpeed: 8
    - _WarpStrength: 0.025
    - _WaveAmount: 7
    - _WaveSpeed: 10
    - _WaveStrength: 7.5
    - _WaveX: 0
    - _WaveY: 0.5
    - _ZTestMode: 4
    - _ZWrite: 1
    - _ZoomUvAmount: 0.5
    m_Colors:
    - _AlphaOutlineColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _ColorChangeNewCol: {r: 1, g: 1, b: 0, a: 1}
    - _ColorChangeNewCol2: {r: 1, g: 1, b: 0, a: 1}
    - _ColorChangeNewCol3: {r: 1, g: 1, b: 0, a: 1}
    - _ColorChangeTarget: {r: 1, g: 0, b: 0, a: 1}
    - _ColorChangeTarget2: {r: 1, g: 0, b: 0, a: 1}
    - _ColorChangeTarget3: {r: 1, g: 0, b: 0, a: 1}
    - _ColorSwapBlue: {r: 1, g: 1, b: 1, a: 1}
    - _ColorSwapGreen: {r: 1, g: 1, b: 1, a: 1}
    - _ColorSwapRed: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _FadeBurnColor: {r: 1, g: 1, b: 0, a: 1}
    - _GlowColor: {r: 1, g: 1, b: 1, a: 1}
    - _GradBotLeftCol: {r: 0, g: 0, b: 1, a: 1}
    - _GradBotRightCol: {r: 0, g: 1, b: 0, a: 1}
    - _GradTopLeftCol: {r: 1, g: 0, b: 0, a: 1}
    - _GradTopRightCol: {r: 1, g: 1, b: 0, a: 1}
    - _GreyscaleTintColor: {r: 1, g: 1, b: 1, a: 1}
    - _HitEffectColor: {r: 1, g: 1, b: 1, a: 1}
    - _HologramStripeColor: {r: 0, g: 1, b: 1, a: 1}
    - _InnerOutlineColor: {r: 1, g: 0, b: 0, a: 1}
    - _OutlineColor: {r: 1, g: 1, b: 1, a: 1}
    - _OverlayColor: {r: 1, g: 1, b: 1, a: 1}
    - _ShadowColor: {r: 0, g: 0, b: 0, a: 1}
    - _ShineColor: {r: 1, g: 1, b: 1, a: 1}
