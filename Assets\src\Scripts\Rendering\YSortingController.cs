using UnityEngine;
using Sirenix.OdinInspector;

[RequireComponent(typeof(SpriteRenderer))]
public class YSortingController : MonoBehaviour
{
    [Title("Y-Sorting Settings")]
    [SerializeField]
    [Tooltip("Offset to adjust the sorting point (useful for tall sprites)")]
    private float yOffset = 0f;
    
    [SerializeField]
    [Tooltip("How often to update sorting (0 = every frame)")]
    [Range(0f, 1f)]
    private float updateInterval = 0.1f;
    
    [SerializeField]
    [Tooltip("Base sorting order (added to Y-based calculation)")]
    private int baseSortingOrder = 0;
    
    [SerializeField]
    [Tooltip("Multiplier for Y position to sorting order conversion")]
    private float sortingPrecision = 100f;
    
    private SpriteRenderer spriteRenderer;
    private float lastUpdateTime;
    private float lastYPosition;
    
    [ShowInInspector, ReadOnly]
    private int currentSortingOrder;
    
    void Awake()
    {
        spriteRenderer = GetComponent<SpriteRenderer>();
        // Ensure we're on the same sorting layer as vegetation
        spriteRenderer.sortingLayerName = "Default";
        UpdateSorting();
    }
    
    void Start()
    {
        // Initial sorting update
        UpdateSorting();
    }
    
    void Update()
    {
        // Only update if enough time has passed or position has changed significantly
        if (updateInterval <= 0f || Time.time - lastUpdateTime >= updateInterval)
        {
            float currentY = transform.position.y + yOffset;
            if (Mathf.Abs(currentY - lastYPosition) > 0.01f)
            {
                UpdateSorting();
                lastYPosition = currentY;
                lastUpdateTime = Time.time;
            }
        }
    }
    
    void UpdateSorting()
    {
        // Calculate sorting order based on Y position
        // Lower Y = higher sorting order (rendered on top)
        float adjustedY = transform.position.y + yOffset;
        currentSortingOrder = baseSortingOrder - Mathf.RoundToInt(adjustedY * sortingPrecision);
        spriteRenderer.sortingOrder = currentSortingOrder;
    }
    
    // Public method to force immediate update
    public void ForceUpdateSorting()
    {
        UpdateSorting();
    }
    
    // Allow runtime adjustment of offset
    public void SetYOffset(float newOffset)
    {
        yOffset = newOffset;
        UpdateSorting();
    }
    
    #if UNITY_EDITOR
    void OnDrawGizmosSelected()
    {
        // Visualize the sorting point
        Vector3 sortingPoint = transform.position + Vector3.up * yOffset;
        Gizmos.color = Color.cyan;
        Gizmos.DrawWireSphere(sortingPoint, 0.1f);
        Gizmos.DrawLine(transform.position, sortingPoint);
    }
    
    [Button("Test Sorting Update")]
    void TestSortingUpdate()
    {
        if (Application.isPlaying)
        {
            UpdateSorting();
            Debug.Log($"Sorting Order: {currentSortingOrder} (Y: {transform.position.y + yOffset})");
        }
    }
    #endif
}