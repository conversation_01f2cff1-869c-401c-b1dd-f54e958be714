%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c6a67a9909fc45446b745e972728f0a8, type: 3}
  m_Name: VegetationPrefab
  m_EditorClassIdentifier: Assembly-CSharp::VegetationPrefabData
  vegetationName: Zone 1
  description: 
  prefab: {fileID: 0}
  spawnChance: 0.3
  minimumSpacing: 0.5
  allowEdgePlacement: 1
  scaleRange: {x: 0.9, y: 1.1}
  randomRotation: 0
  rotationRange: {x: 0, y: 360}
  allowRandomFlip: 1
  ySortingOffset: 0
  sortingOrderOffset: 0
  maxPerChunk: 50
  initialPoolSize: 100
  poolGrowSize: 20
