using UnityEngine;
using System.Collections.Generic;
using Sirenix.OdinInspector;

[CreateAssetMenu(fileName = "PlayerBuff", menuName = "2D Rogue/Buffs/Player Buff")]
public class PlayerBuffData : ScriptableObject
{
    [Title("Buff Information")]
    [HorizontalGroup("Info", 0.15f)]
    [PreviewField(80, ObjectFieldAlignment.Left)]
    [HideLabel]
    public Sprite icon;
    
    [VerticalGroup("Info/Details")]
    [LabelText("Buff Name")]
    public string buffName = "New Buff";
    
    [VerticalGroup("Info/Details")]
    [TextArea(2, 3)]
    public string description = "Buff description";
    
    [VerticalGroup("Info/Details")]
    [EnumToggleButtons]
    public GemRarity rarity = GemRarity.Common;
    
    [Title("Buff Modifiers")]
    [InfoBox("Configure the stat modifiers this buff provides")]
    [ListDrawerSettings(
        DraggableItems = true,
        ShowIndexLabels = false,
        CustomAddFunction = "AddModifier"
    )]
    public List<StatModifier> modifiers = new List<StatModifier>();
    
    [Title("Special Properties")]
    [PropertySpace]
    [InfoBox("Special mechanics that can't be expressed as simple stat modifiers")]
    public bool grantsLifeOnKill = false;
    [ShowIf("grantsLifeOnKill")]
    [Indent]
    public int lifeOnKillAmount = 5;
    
    public bool grantsManaOnKill = false;
    [ShowIf("grantsManaOnKill")]
    [Indent]
    public int manaOnKillAmount = 10;
    
    public bool grantsExplosionOnKill = false;
    [ShowIf("grantsExplosionOnKill")]
    [Indent]
    public float explosionRadius = 3f;
    [ShowIf("grantsExplosionOnKill")]
    [Indent]
    public int explosionDamage = 50;
    
    [Title("Visual")]
    public Color buffColor = Color.white;
    
    // Get display name with rarity color and formatting (matching gem formatting)
    public string GetDisplayName()
    {
        Color rarityColor = GetRarityColor();
        return $"<size=120%><b><color=#{ColorUtility.ToHtmlStringRGB(rarityColor)}>{buffName}</color></b></size>";
    }
    
    // Get rarity color
    public Color GetRarityColor()
    {
        return RarityUtility.GetRarityColor(rarity);
    }
    
    // Get formatted description for UI (matching gem formatting)
    public string GetFormattedDescription()
    {
        // Title with rarity color
        string tooltip = $"<size=120%><b><color=#{ColorUtility.ToHtmlStringRGB(GetRarityColor())}>{buffName}</color></b></size>";
        tooltip += $"\n{rarity} Permanent Buff";

        if (!string.IsNullOrEmpty(description))
        {
            tooltip += $"\n\n{description}";
        }

        // Add modifier descriptions
        if (modifiers.Count > 0)
        {
            tooltip += "\n\n" + TooltipFormatter.FormatSectionHeader("Stats");
            foreach (var mod in modifiers)
            {
                // Format modifiers with colors like gems do
                string modifierText = GetFormattedModifier(mod);
                tooltip += $"\n{modifierText}";
            }
        }

        // Add special properties
        if (grantsLifeOnKill || grantsManaOnKill || grantsExplosionOnKill)
        {
            tooltip += "\n\n" + TooltipFormatter.FormatSectionHeader("Special Effects");

            if (grantsLifeOnKill)
                tooltip += $"\n<color={TooltipFormatter.LIFE_COLOR}>Gain {lifeOnKillAmount} life on kill</color>";

            if (grantsManaOnKill)
                tooltip += $"\n<color={TooltipFormatter.MANA_COLOR}>Gain {manaOnKillAmount} mana on kill</color>";

            if (grantsExplosionOnKill)
                tooltip += $"\n<color={TooltipFormatter.EXPLOSION_COLOR}>Enemies explode on death for {explosionDamage} damage</color>";
        }

        return tooltip;
    }
    
    private string GetFormattedModifier(StatModifier mod)
    {
        string sign = mod.value >= 0 ? "+" : "";
        string suffix = mod.modifierType == StatModifier.ModifierType.Flat ? "" : "%";
        string modTypeText = mod.modifierType == StatModifier.ModifierType.More ? " more" : "";
        
        if (mod.statType.IsPercentageStat() && mod.modifierType == StatModifier.ModifierType.Flat)
        {
            suffix = "%";
        }
        
        // Simplified color scheme: only "more" gets gold, everything else is gray
        string color = TooltipFormatter.LABEL_COLOR.Substring(1); // Default gray for ALL stats

        if (mod.modifierType == StatModifier.ModifierType.More)
        {
            color = TooltipFormatter.HEADER_COLOR.Substring(1); // Gold ONLY for more multipliers
        }
        // All other modifiers (flat and increased) stay gray

        return $"<color=#{color}>{sign}{mod.value:F1}{suffix}{modTypeText} {mod.statType.GetDisplayName()}</color>";
    }
    
    // Editor helper
    private StatModifier AddModifier()
    {
        return new StatModifier(StatType.MaxHealth, StatModifier.ModifierType.Increased, 10f, buffName);
    }
    
    #if UNITY_EDITOR
    [Title("Editor Tools")]
    [ButtonGroup("Presets")]
    [Button("Offensive Buff")]
    private void PresetOffensive()
    {
        buffName = "Berserker's Might";
        description = "Raw power courses through your veins.";
        rarity = GemRarity.Rare;
        modifiers.Clear();
        modifiers.Add(new StatModifier(StatType.DamageIncreased, StatModifier.ModifierType.Increased, 15f, buffName));
        modifiers.Add(new StatModifier(StatType.AttackSpeed, StatModifier.ModifierType.Increased, 10f, buffName));
        modifiers.Add(new StatModifier(StatType.CriticalChance, StatModifier.ModifierType.Flat, 5f, buffName));
        UnityEditor.EditorUtility.SetDirty(this);
    }
    
    [ButtonGroup("Presets")]
    [Button("Defensive Buff")]
    private void PresetDefensive()
    {
        buffName = "Stone Skin";
        description = "Your skin hardens like stone.";
        rarity = GemRarity.Uncommon;
        modifiers.Clear();
        modifiers.Add(new StatModifier(StatType.Defense, StatModifier.ModifierType.Flat, 15f, buffName));
        modifiers.Add(new StatModifier(StatType.MaxHealth, StatModifier.ModifierType.Increased, 20f, buffName));
        modifiers.Add(new StatModifier(StatType.HealthRegen, StatModifier.ModifierType.Flat, 2f, buffName));
        UnityEditor.EditorUtility.SetDirty(this);
    }
    
    [ButtonGroup("Presets")]
    [Button("Utility Buff")]
    private void PresetUtility()
    {
        buffName = "Swift Winds";
        description = "The wind carries you forward.";
        rarity = GemRarity.Common;
        modifiers.Clear();
        modifiers.Add(new StatModifier(StatType.MoveSpeed, StatModifier.ModifierType.Increased, 25f, buffName));
        modifiers.Add(new StatModifier(StatType.DodgeChance, StatModifier.ModifierType.Flat, 10f, buffName));
        modifiers.Add(new StatModifier(StatType.ManaRegen, StatModifier.ModifierType.Increased, 50f, buffName));
        UnityEditor.EditorUtility.SetDirty(this);
    }
    #endif
}