using UnityEngine;

/// <summary>
/// Interface for items that can be selected in the selection UI (gems, buffs, etc.)
/// </summary>
public interface ISelectable
{
    string GetDisplayName();
    string GetFormattedDescription();
    Sprite GetIcon();
    Color GetRarityColor();
    SelectionType GetSelectionType();
}

public enum SelectionType
{
    SkillGem,
    SupportGem,
    PlayerBuff
}

/// <summary>
/// Wrapper for GemInstance to implement ISelectable
/// </summary>
public class SelectableGem : ISelectable
{
    private GemInstance gem;
    
    public SelectableGem(GemInstance gemInstance)
    {
        gem = gemInstance;
    }
    
    public GemInstance GetGem() => gem;
    
    public string GetDisplayName() => gem.DisplayName;
    
    public string GetFormattedDescription()
    {
        string description = "";
        
        // Gem Type with color
        if (gem.IsSkillGem)
        {
            description += "<color=#CC3333><b>Skill Gem</b></color>\n";
        }
        else if (gem.IsSupportGem)
        {
            description += "<color=#33CC33><b>Support Gem</b></color>\n";
        }
        
        description += "\n";
        
        // Description
        if (gem.gemDataTemplate != null && !string.IsNullOrEmpty(gem.gemDataTemplate.description))
        {
            description += $"{gem.gemDataTemplate.description}\n\n";
        }
        
        // Stats
        description += "<size=110%><b><color=#FFD700>Stats</color></b></size>\n";
        description += GetFormattedGemStats();
        
        return description;
    }
    
    private string GetFormattedGemStats()
    {
        if (gem.IsSkillGem && gem.gemDataTemplate is SkillGemData skillGem)
        {
            string stats = TooltipFormatter.FormatStat("Damage", $"{gem.GetSkillDamage():F0} ({skillGem.damageType})") + "\n" +
                          TooltipFormatter.FormatStat("Cooldown", gem.GetSkillCooldown(), "F1", "s") + "\n" +
                          TooltipFormatter.FormatStat("Mana Cost", gem.GetSkillManaCost(), "F0") + "\n" +
                          TooltipFormatter.FormatStat("Support Slots", gem.GetSupportSlotCount());

            // Add status effect information if ailment chance > 0
            if (skillGem.ailmentChance > 0)
            {
                stats += $"\n\n<color={TooltipFormatter.HEADER_COLOR}>Status Effects ({skillGem.ailmentChance:F0}% chance):</color>";

                switch (skillGem.damageType)
                {
                    case DamageType.Fire:
                        // Use default values if not set (for backward compatibility)
                        float ignitePercentToUse = skillGem.ignitePercent > 0 ? skillGem.ignitePercent : 0.2f;
                        float igniteDurationToUse = skillGem.igniteDuration > 0 ? skillGem.igniteDuration : 4f;
                        float totalIgniteDamage = ignitePercentToUse * 100f;
                        stats += "\n" + TooltipFormatter.FormatStat("• Ignite Damage", totalIgniteDamage, "F0", "%");
                        stats += "\n" + TooltipFormatter.FormatStat("• Ignite Duration", igniteDurationToUse, "F1", "s");
                        break;

                    case DamageType.Ice:
                        float freezeSlowToUse = skillGem.freezeSlowAmount > 0 ? skillGem.freezeSlowAmount : 0.5f;
                        float freezeDurationToUse = skillGem.freezeDuration > 0 ? skillGem.freezeDuration : 2f;
                        float slowPercent = freezeSlowToUse * 100f;
                        stats += "\n" + TooltipFormatter.FormatStat("• Freeze Slow", slowPercent, "F0", "%");
                        stats += "\n" + TooltipFormatter.FormatStat("• Freeze Duration", freezeDurationToUse, "F1", "s");
                        break;

                    case DamageType.Physical:
                        float bleedPercentToUse = skillGem.bleedPercent > 0 ? skillGem.bleedPercent : 0.15f;
                        float bleedDurationToUse = skillGem.bleedDuration > 0 ? skillGem.bleedDuration : 6f;
                        float totalBleedDamage = bleedPercentToUse * 100f;
                        stats += "\n" + TooltipFormatter.FormatStat("• Bleed Damage", totalBleedDamage, "F0", "%");
                        stats += "\n" + TooltipFormatter.FormatStat("• Bleed Duration", bleedDurationToUse, "F1", "s");
                        break;

                    case DamageType.Lightning:
                        float shockChainToUse = skillGem.shockChainDamage > 0 ? skillGem.shockChainDamage : 0.1f;
                        float shockRangeToUse = skillGem.shockChainRange > 0 ? skillGem.shockChainRange : 3f;
                        float shockDurationToUse = skillGem.shockDuration > 0 ? skillGem.shockDuration : 2f;
                        float chainPercent = shockChainToUse * 100f;
                        stats += "\n" + TooltipFormatter.FormatStat("• Shock Damage", chainPercent, "F0", "%");
                        stats += "\n" + TooltipFormatter.FormatStat("• Shock Range", shockRangeToUse, "F1", "m");
                        stats += "\n" + TooltipFormatter.FormatStat("• Shock Duration", shockDurationToUse, "F1", "s");
                        break;
                }
            }

            return stats;
        }
        else if (gem.IsSupportGem && gem.gemDataTemplate is SupportGemData supportGem)
        {
            string stats = "";

            if (supportGem.damageIncreased != 0f)
                stats += TooltipFormatter.FormatStat("Increased Damage", $"{supportGem.damageIncreased:+0;-0}%") + "\n";

            if (supportGem.damageMore != 1f)
                stats += TooltipFormatter.FormatStat("More Damage", $"{(supportGem.damageMore - 1f) * 100:+0;-0}%") + "\n";

            if (supportGem.cooldownMultiplier != 1f)
                stats += TooltipFormatter.FormatStat("Cooldown", $"{(supportGem.cooldownMultiplier - 1f) * 100:+0;-0}%") + "\n";

            if (supportGem.manaCostMultiplier != 1f)
                stats += TooltipFormatter.FormatStat("Mana Cost", $"{(supportGem.manaCostMultiplier - 1f) * 100:+0;-0}%") + "\n";

            if (supportGem.attackSpeedMultiplier != 1f)
                stats += TooltipFormatter.FormatStat("Attack Speed", $"{(supportGem.attackSpeedMultiplier - 1f) * 100:+0;-0}%") + "\n";

            if (supportGem.addedCritChance != 0f)
                stats += TooltipFormatter.FormatStat("Crit Chance", $"+{supportGem.addedCritChance:F1}%") + "\n";

            if (supportGem.critMultiplierModifier != 1f)
                stats += TooltipFormatter.FormatStat("Crit Multiplier", $"{(supportGem.critMultiplierModifier - 1f) * 100:+0;-0}%") + "\n";
            
            // Special effects
            if (supportGem.addsPierce)
                stats += TooltipFormatter.FormatSpecialEffect("Pierce", "Enabled") + "\n";

            if (supportGem.addsChain)
                stats += TooltipFormatter.FormatSpecialEffect("Chain", $"+{supportGem.chainCount} Times") + "\n";

            if (supportGem.addsAreaDamage)
                stats += TooltipFormatter.FormatSpecialEffect("Area Damage", $"Radius {supportGem.areaRadius}") + "\n";

            if (supportGem.addsFork)
                stats += TooltipFormatter.FormatSpecialEffect("Fork", $"{gem.GetForkCount()} projectiles ({supportGem.forkAngle}° spread)") + "\n";

            if (supportGem.addsMultipleProjectiles)
            {
                string projectileText = $"+{gem.GetExtraProjectiles()}";
                if (supportGem.projectileSpreadAngle > 0)
                    projectileText += $" ({supportGem.projectileSpreadAngle}° spread)";
                stats += TooltipFormatter.FormatSpecialEffect("Projectiles", projectileText) + "\n";
            }

            // Add Spell Echo support (this was missing!)
            if (supportGem.addsSpellEcho)
            {
                int echoCount = gem.GetSpellEchoCount();
                stats += TooltipFormatter.FormatSpecialEffect("Recast Times", echoCount.ToString()) + "\n";
                if (supportGem.echoSpreadRadius > 0)
                    stats += TooltipFormatter.FormatSpecialEffect("Recast Radius", supportGem.echoSpreadRadius.ToString()) + "\n";
            }

            // Status effect modifiers
            bool hasStatusEffectModifiers = supportGem.igniteEffectivenessMultiplier != 1f || supportGem.igniteDurationMultiplier != 1f ||
                                           supportGem.freezeEffectivenessMultiplier != 1f || supportGem.freezeDurationMultiplier != 1f ||
                                           supportGem.bleedEffectivenessMultiplier != 1f || supportGem.bleedDurationMultiplier != 1f ||
                                           supportGem.shockEffectivenessMultiplier != 1f || supportGem.shockRangeMultiplier != 1f;

            if (hasStatusEffectModifiers)
            {
                stats += $"\n<color={TooltipFormatter.HEADER_COLOR}>Status Effect Modifiers:</color>\n";

                // Ignite modifiers
                if (supportGem.igniteEffectivenessMultiplier != 1f)
                    stats += TooltipFormatter.FormatPercentageModifier("• Ignite Damage", supportGem.igniteEffectivenessMultiplier) + "\n";
                if (supportGem.igniteDurationMultiplier != 1f)
                    stats += TooltipFormatter.FormatPercentageModifier("• Ignite Duration", supportGem.igniteDurationMultiplier) + "\n";

                // Freeze modifiers
                if (supportGem.freezeEffectivenessMultiplier != 1f)
                    stats += TooltipFormatter.FormatPercentageModifier("• Freeze Effectiveness", supportGem.freezeEffectivenessMultiplier) + "\n";
                if (supportGem.freezeDurationMultiplier != 1f)
                    stats += TooltipFormatter.FormatPercentageModifier("• Freeze Duration", supportGem.freezeDurationMultiplier) + "\n";

                // Bleed modifiers
                if (supportGem.bleedEffectivenessMultiplier != 1f)
                    stats += TooltipFormatter.FormatPercentageModifier("• Bleed Damage", supportGem.bleedEffectivenessMultiplier) + "\n";
                if (supportGem.bleedDurationMultiplier != 1f)
                    stats += TooltipFormatter.FormatPercentageModifier("• Bleed Duration", supportGem.bleedDurationMultiplier) + "\n";

                // Shock modifiers
                if (supportGem.shockEffectivenessMultiplier != 1f)
                    stats += TooltipFormatter.FormatPercentageModifier("• Shock Damage", supportGem.shockEffectivenessMultiplier) + "\n";
                if (supportGem.shockRangeMultiplier != 1f)
                    stats += TooltipFormatter.FormatPercentageModifier("• Shock Range", supportGem.shockRangeMultiplier) + "\n";
            }

            // Random modifiers
            if (gem.randomModifiers.Count > 0)
            {
                stats += "\n" + TooltipFormatter.FormatSectionHeader("Modifiers") + "\n";
                foreach (var modifier in gem.randomModifiers)
                {
                    stats += $"{modifier.GetDisplayString()}\n";
                }
            }

            return stats.TrimEnd('\n');
        }
        
        return "";
    }
    
    public Sprite GetIcon() => gem.gemDataTemplate?.icon;
    
    public Color GetRarityColor() => gem.RarityColor;
    
    public SelectionType GetSelectionType()
    {
        return gem.IsSkillGem ? SelectionType.SkillGem : SelectionType.SupportGem;
    }
}

/// <summary>
/// Wrapper for PlayerBuffData to implement ISelectable
/// </summary>
public class SelectableBuff : ISelectable
{
    private PlayerBuffData buff;
    
    public SelectableBuff(PlayerBuffData buffData)
    {
        buff = buffData;
    }
    
    public PlayerBuffData GetBuff() => buff;
    
    public string GetDisplayName() => buff.GetDisplayName();
    
    public string GetFormattedDescription() => buff.GetFormattedDescription();
    
    public Sprite GetIcon() => buff.icon;
    
    public Color GetRarityColor() => buff.GetRarityColor();
    
    public SelectionType GetSelectionType() => SelectionType.PlayerBuff;
}