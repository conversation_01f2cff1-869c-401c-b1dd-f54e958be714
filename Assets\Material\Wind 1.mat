%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Wind 1
  m_Shader: {fileID: 4800000, guid: 84e903626e0ef2f40a8dd08a583cda95, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - PIXELSNAP_ON
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AlphaTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _WindNoiseTex:
        m_Texture: {fileID: 2800000, guid: 1a748330fd1f90e448ec7b4fb53ed8af, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - PixelSnap: 1
    - _BaseInfluence: 0
    - _EnableExternalAlpha: 0
    - _TopInfluence: 0.177
    - _WindNoiseScale: 1.02
    - _WindPulseAmplitude: 0.2
    - _WindPulseFrequency: 0.93
    - _WindSpeed: 2
    - _WindStrength: 0.222
    m_Colors:
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _Flip: {r: 1, g: 1, b: 1, a: 1}
    - _RendererColor: {r: 1, g: 1, b: 1, a: 1}
    - _WindDirection: {r: 1, g: 0, b: 0, a: 0}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
