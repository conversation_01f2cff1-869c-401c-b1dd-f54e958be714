using System.Text;
using UnityEngine;

/// <summary>
/// Centralized utility class for consistent tooltip formatting across all systems.
/// Provides standardized methods for formatting stats, status effects, headers, and special effects.
/// Eliminates code duplication and ensures visual consistency throughout the UI.
/// </summary>
public static class TooltipFormatter
{
    #region Color Constants
    
    /// <summary>Gray color for all stat labels and standard text</summary>
    public const string LABEL_COLOR = "#CCCCCC";
    
    /// <summary>Gold color for section headers</summary>
    public const string HEADER_COLOR = "#FFD700";
    
    /// <summary>White color for values (applied via bold formatting)</summary>
    public const string VALUE_COLOR = "#FFFFFF";
    
    /// <summary>Green color for increased damage modifiers</summary>
    public const string INCREASED_DAMAGE_COLOR = "#90EE90";
    
    /// <summary>Light blue color for mana-related effects</summary>
    public const string MANA_COLOR = "#87CEEB";
    
    /// <summary>Red color for life-related effects</summary>
    public const string LIFE_COLOR = "#90EE90";
    
    /// <summary>Orange color for explosion effects</summary>
    public const string EXPLOSION_COLOR = "#FF6347";
    
    /// <summary>Dark red color for corruption status</summary>
    public const string CORRUPTION_COLOR = "#8B0000";
    
    /// <summary>Red color for skill gem type</summary>
    public const string SKILL_GEM_COLOR = "#CC3333";
    
    /// <summary>Green color for support gem type</summary>
    public const string SUPPORT_GEM_COLOR = "#33CC33";
    
    #endregion
    
    #region Status Effect Colors
    
    /// <summary>Orange color for fire/ignite status effects</summary>
    public const string IGNITE_COLOR = "#FF6B35";
    
    /// <summary>Light blue color for ice/freeze status effects</summary>
    public const string FREEZE_COLOR = "#4FC3F7";
    
    /// <summary>Dark red color for physical/bleed status effects</summary>
    public const string BLEED_COLOR = "#8B0000";
    
    /// <summary>Yellow color for lightning/shock status effects</summary>
    public const string SHOCK_COLOR = "#FFE082";
    
    #endregion
    
    #region Basic Formatting Methods
    
    /// <summary>
    /// Format a basic stat with label and value using standardized colors.
    /// </summary>
    /// <param name="label">The stat label (e.g., "Damage", "Cooldown")</param>
    /// <param name="value">The stat value</param>
    /// <param name="unit">Optional unit suffix (e.g., "s", "%", "x")</param>
    /// <returns>Formatted stat string: "Label: Value"</returns>
    public static string FormatStat(string label, object value, string unit = "")
    {
        return $"<color={LABEL_COLOR}>{label}:</color> <b>{value}{unit}</b>";
    }
    
    /// <summary>
    /// Format a stat with custom value formatting.
    /// </summary>
    /// <param name="label">The stat label</param>
    /// <param name="value">The numeric value</param>
    /// <param name="format">Format string for the value (e.g., "F1", "F0")</param>
    /// <param name="unit">Optional unit suffix</param>
    /// <returns>Formatted stat string</returns>
    public static string FormatStat(string label, float value, string format, string unit = "")
    {
        return $"<color={LABEL_COLOR}>{label}:</color> <b>{value.ToString(format)}{unit}</b>";
    }
    
    /// <summary>
    /// Format a percentage modifier (e.g., support gem modifiers).
    /// </summary>
    /// <param name="label">The modifier label</param>
    /// <param name="multiplier">The multiplier value (1.0 = no change)</param>
    /// <returns>Formatted percentage modifier: "Label: +X%"</returns>
    public static string FormatPercentageModifier(string label, float multiplier)
    {
        float percentage = (multiplier - 1f) * 100f;
        return $"<color={LABEL_COLOR}>{label}:</color> <b>{percentage:+0;-0}%</b>";
    }
    
    /// <summary>
    /// Format a section header with standardized styling.
    /// </summary>
    /// <param name="title">The section title</param>
    /// <returns>Formatted header string</returns>
    public static string FormatSectionHeader(string title)
    {
        return $"<size=110%><b><color={HEADER_COLOR}>{title}</color></b></size>";
    }
    
    /// <summary>
    /// Format a special effect with label and description.
    /// </summary>
    /// <param name="label">The effect label</param>
    /// <param name="description">The effect description</param>
    /// <returns>Formatted special effect string</returns>
    public static string FormatSpecialEffect(string label, string description)
    {
        return $"<color={LABEL_COLOR}>{label}:</color> <b>{description}</b>";
    }
    
    #endregion
    
    #region Status Effect Formatting
    
    /// <summary>
    /// Format status effect information with standardized colors and layout.
    /// </summary>
    /// <param name="effectType">The type of status effect</param>
    /// <param name="damagePercent">Damage percentage (for DoT effects)</param>
    /// <param name="duration">Effect duration in seconds</param>
    /// <param name="additionalInfo">Additional effect information (e.g., range for shock)</param>
    /// <returns>Formatted status effect string</returns>
    public static string FormatStatusEffect(StatusEffectType effectType, float damagePercent = 0f, float duration = 0f, string additionalInfo = "")
    {
        StringBuilder sb = new StringBuilder();
        
        switch (effectType)
        {
            case StatusEffectType.Ignite:
                if (damagePercent > 0) sb.AppendLine(FormatStat("• Ignite Damage", damagePercent, "F0", "%"));
                if (duration > 0) sb.AppendLine(FormatStat("• Ignite Duration", duration, "F1", "s"));
                break;
                
            case StatusEffectType.Freeze:
                if (damagePercent > 0) sb.AppendLine(FormatStat("• Freeze Slow", damagePercent, "F0", "%"));
                if (duration > 0) sb.AppendLine(FormatStat("• Freeze Duration", duration, "F1", "s"));
                break;
                
            case StatusEffectType.Bleed:
                if (damagePercent > 0) sb.AppendLine(FormatStat("• Bleed Damage", damagePercent, "F0", "%"));
                if (duration > 0) sb.AppendLine(FormatStat("• Bleed Duration", duration, "F1", "s"));
                break;
                
            case StatusEffectType.Shock:
                if (damagePercent > 0) sb.AppendLine(FormatStat("• Shock Damage", damagePercent, "F0", "%"));
                if (!string.IsNullOrEmpty(additionalInfo)) sb.AppendLine(FormatStat("• Shock Range", additionalInfo));
                if (duration > 0) sb.AppendLine(FormatStat("• Shock Duration", duration, "F1", "s"));
                break;
        }
        
        return sb.ToString().TrimEnd('\r', '\n');
    }
    
    /// <summary>
    /// Format status effect modifier (for support gems).
    /// </summary>
    /// <param name="effectType">The type of status effect</param>
    /// <param name="modifierType">The type of modifier (damage, duration, etc.)</param>
    /// <param name="multiplier">The multiplier value</param>
    /// <returns>Formatted status effect modifier string</returns>
    public static string FormatStatusEffectModifier(StatusEffectType effectType, string modifierType, float multiplier)
    {
        string effectName = effectType.ToString();
        return FormatPercentageModifier($"• {effectName} {modifierType}", multiplier);
    }
    
    #endregion
    
    #region Gem-Specific Formatting
    
    /// <summary>
    /// Format gem header with rarity color and type.
    /// </summary>
    /// <param name="gemName">The gem name</param>
    /// <param name="rarity">The gem rarity</param>
    /// <param name="isSkillGem">Whether this is a skill gem (vs support gem)</param>
    /// <returns>Formatted gem header</returns>
    public static string FormatGemHeader(string gemName, GemRarity rarity, bool isSkillGem)
    {
        Color rarityColor = RarityUtility.GetRarityColor(rarity);
        string hexColor = ColorUtility.ToHtmlStringRGB(rarityColor);
        string gemType = isSkillGem ? "Skill" : "Support";
        
        return $"<size=120%><b><color=#{hexColor}>{gemName}</color></b></size>\n{rarity} {gemType} Gem";
    }
    
    /// <summary>
    /// Format gem type indicator for selection UI.
    /// </summary>
    /// <param name="isSkillGem">Whether this is a skill gem</param>
    /// <returns>Formatted gem type string</returns>
    public static string FormatGemType(bool isSkillGem)
    {
        if (isSkillGem)
            return $"<color={SKILL_GEM_COLOR}><b>Skill Gem</b></color>";
        else
            return $"<color={SUPPORT_GEM_COLOR}><b>Support Gem</b></color>";
    }
    
    #endregion
    
    #region Utility Methods
    
    /// <summary>
    /// Add a newline separator between sections.
    /// </summary>
    /// <returns>Double newline for section separation</returns>
    public static string SectionSeparator => "\n\n";
    
    /// <summary>
    /// Add a single newline.
    /// </summary>
    /// <returns>Single newline</returns>
    public static string LineSeparator => "\n";
    
    /// <summary>
    /// Format corruption status indicator.
    /// </summary>
    /// <returns>Formatted corruption warning</returns>
    public static string FormatCorruptionStatus()
    {
        return $"<b><color={CORRUPTION_COLOR}>Corrupted - Cannot be modified</color></b>";
    }
    
    #endregion

    #region Advanced Formatting Methods

    /// <summary>
    /// Format skill gem basic stats section.
    /// </summary>
    /// <param name="damage">Damage value</param>
    /// <param name="damageType">Type of damage</param>
    /// <param name="cooldown">Cooldown in seconds</param>
    /// <param name="manaCost">Mana cost</param>
    /// <param name="attackSpeed">Attack speed multiplier</param>
    /// <param name="critChance">Critical hit chance percentage</param>
    /// <param name="critMultiplier">Critical hit multiplier</param>
    /// <param name="supportSlots">Number of support slots</param>
    /// <returns>Formatted skill gem stats section</returns>
    public static string FormatSkillGemStats(float damage, DamageType damageType, float cooldown, float manaCost,
        float attackSpeed, float critChance, float critMultiplier, int supportSlots)
    {
        StringBuilder sb = new StringBuilder();
        sb.Append(FormatSectionHeader("Stats"));
        sb.Append("\n");
        sb.Append(FormatStat("Damage", damage, "F0"));
        sb.Append("\n");
        sb.Append(FormatStat("Cooldown", cooldown, "F1", "s"));
        sb.Append("\n");
        sb.Append(FormatStat("Mana Cost", manaCost, "F0"));
        sb.Append("\n");
        sb.Append(FormatStat("Attack Speed", attackSpeed, "F1", "x"));
        sb.Append("\n");
        sb.Append(FormatStat("Critical Chance", critChance, "F1", "%"));
        sb.Append("\n");
        sb.Append(FormatStat("Critical Multiplier", critMultiplier, "F1", "x"));
        sb.Append("\n");
        sb.Append(FormatStat("Support Slots", supportSlots));
        return sb.ToString();
    }

    /// <summary>
    /// Format support gem damage modifiers section.
    /// </summary>
    /// <param name="increasedDamage">Increased damage percentage</param>
    /// <param name="moreDamage">More damage multiplier</param>
    /// <returns>Formatted damage modifiers or empty string if no modifiers</returns>
    public static string FormatSupportGemDamageModifiers(float increasedDamage, float moreDamage)
    {
        StringBuilder sb = new StringBuilder();
        bool hasModifiers = false;

        if (increasedDamage != 0f)
        {
            if (!hasModifiers) { sb.AppendLine(FormatSectionHeader("Stats")); hasModifiers = true; }
            sb.AppendLine(FormatStat("Increased Damage", $"{increasedDamage:+0;-0}%"));
        }

        if (moreDamage != 1f)
        {
            if (!hasModifiers) { sb.AppendLine(FormatSectionHeader("Stats")); hasModifiers = true; }
            float percentage = (moreDamage - 1f) * 100f;
            sb.Append(FormatStat("More Damage", $"{percentage:+0;-0}%"));
        }

        return hasModifiers ? sb.ToString() : "";
    }

    /// <summary>
    /// Format special effects section for support gems.
    /// </summary>
    /// <param name="pierce">Whether adds pierce</param>
    /// <param name="chainCount">Number of chain bounces (0 if no chain)</param>
    /// <param name="areaRadius">Area damage radius (0 if no area)</param>
    /// <param name="forkCount">Number of fork projectiles (0 if no fork)</param>
    /// <param name="forkAngle">Fork spread angle</param>
    /// <param name="extraProjectiles">Number of extra projectiles (0 if none)</param>
    /// <param name="projectileSpread">Projectile spread angle</param>
    /// <param name="echoCount">Spell echo count (0 if no echo)</param>
    /// <param name="echoRadius">Echo spread radius</param>
    /// <returns>Formatted special effects section or empty string if no effects</returns>
    public static string FormatSpecialEffects(bool pierce = false, int chainCount = 0, float areaRadius = 0f,
        int forkCount = 0, float forkAngle = 0f, int extraProjectiles = 0, float projectileSpread = 0f,
        int echoCount = 0, float echoRadius = 0f)
    {
        StringBuilder sb = new StringBuilder();
        bool hasEffects = false;

        if (pierce)
        {
            if (!hasEffects) { sb.AppendLine(FormatSectionHeader("Base Mods")); hasEffects = true; }
            sb.AppendLine(FormatSpecialEffect("Pierce", "Enabled"));
        }

        if (chainCount > 0)
        {
            if (!hasEffects) { sb.AppendLine(FormatSectionHeader("Base Mods")); hasEffects = true; }
            sb.AppendLine(FormatSpecialEffect("Chain", $"+{chainCount} Times"));
        }

        if (areaRadius > 0)
        {
            if (!hasEffects) { sb.AppendLine(FormatSectionHeader("Base Mods")); hasEffects = true; }
            sb.AppendLine(FormatSpecialEffect("Area Damage", $"Radius {areaRadius:F1}"));
        }

        if (forkCount > 0)
        {
            if (!hasEffects) { sb.AppendLine(FormatSectionHeader("Base Mods")); hasEffects = true; }
            sb.AppendLine(FormatSpecialEffect("Fork", $"{forkCount} projectiles ({forkAngle}° spread)"));
        }

        if (extraProjectiles > 0)
        {
            if (!hasEffects) { sb.AppendLine(FormatSectionHeader("Base Mods")); hasEffects = true; }
            string projectileText = $"+{extraProjectiles}";
            if (projectileSpread > 0) projectileText += $" ({projectileSpread}° spread)";
            sb.AppendLine(FormatSpecialEffect("Projectiles", projectileText));
        }

        if (echoCount > 0)
        {
            if (!hasEffects) { sb.AppendLine(FormatSectionHeader("Base Mods")); hasEffects = true; }
            sb.AppendLine(FormatSpecialEffect("Recast Times", echoCount.ToString()));
            if (echoRadius > 0)
                sb.AppendLine(FormatSpecialEffect("Recast Radius", echoRadius.ToString()));
        }

        return hasEffects ? sb.ToString().TrimEnd('\r', '\n') : "";
    }

    /// <summary>
    /// Format player buff special effects section.
    /// </summary>
    /// <param name="lifeOnKill">Life gained on kill (0 if none)</param>
    /// <param name="manaOnKill">Mana gained on kill (0 if none)</param>
    /// <param name="explosionDamage">Explosion damage on kill (0 if none)</param>
    /// <returns>Formatted special effects section or empty string if no effects</returns>
    public static string FormatBuffSpecialEffects(float lifeOnKill = 0f, float manaOnKill = 0f, float explosionDamage = 0f)
    {
        StringBuilder sb = new StringBuilder();
        bool hasEffects = false;

        if (lifeOnKill > 0)
        {
            if (!hasEffects) { sb.AppendLine(FormatSectionHeader("Special Effects")); hasEffects = true; }
            sb.AppendLine($"<color={LIFE_COLOR}>Gain {lifeOnKill} life on kill</color>");
        }

        if (manaOnKill > 0)
        {
            if (!hasEffects) { sb.AppendLine(FormatSectionHeader("Special Effects")); hasEffects = true; }
            sb.AppendLine($"<color={MANA_COLOR}>Gain {manaOnKill} mana on kill</color>");
        }

        if (explosionDamage > 0)
        {
            if (!hasEffects) { sb.AppendLine(FormatSectionHeader("Special Effects")); hasEffects = true; }
            sb.Append($"<color={EXPLOSION_COLOR}>Enemies explode on death for {explosionDamage} damage</color>");
        }

        return hasEffects ? sb.ToString() : "";
    }

    #endregion
}
