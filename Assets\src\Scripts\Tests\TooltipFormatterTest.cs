using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// Test class for validating TooltipFormatter functionality and output consistency.
/// Ensures the centralized formatter produces identical output to the original implementations.
/// </summary>
public class TooltipFormatterTest : MonoBehaviour
{
    [Title("Tooltip Formatter Tests")]
    [InfoBox("These tests validate that the centralized TooltipFormatter produces identical output to the original tooltip implementations.")]
    
    [<PERSON><PERSON>("Test Basic Stat Formatting")]
    [InfoBox("Tests basic stat formatting methods")]
    public void TestBasicStatFormatting()
    {
        Debug.Log("=== Basic Stat Formatting Tests ===");
        
        // Test basic stat formatting
        string damageFormat = TooltipFormatter.FormatStat("Damage", 25.0f, "F0");
        string expectedDamage = "<color=#CCCCCC>Damage:</color> <b>25</b>";
        Debug.Log($"Damage Format: {damageFormat}");
        Debug.Log($"Expected: {expectedDamage}");
        Debug.Log($"Match: {damageFormat == expectedDamage}");
        
        // Test stat with unit
        string cooldownFormat = TooltipFormatter.FormatStat("Cooldown", 1.5f, "F1", "s");
        string expectedCooldown = "<color=#CCCCCC>Cooldown:</color> <b>1.5s</b>";
        Debug.Log($"Cooldown Format: {cooldownFormat}");
        Debug.Log($"Expected: {expectedCooldown}");
        Debug.Log($"Match: {cooldownFormat == expectedCooldown}");
        
        // Test percentage modifier
        string modifierFormat = TooltipFormatter.FormatPercentageModifier("Ignite Damage", 1.5f);
        string expectedModifier = "<color=#CCCCCC>Ignite Damage:</color> <b>+50%</b>";
        Debug.Log($"Modifier Format: {modifierFormat}");
        Debug.Log($"Expected: {expectedModifier}");
        Debug.Log($"Match: {modifierFormat == expectedModifier}");
    }
    
    [Button("Test Section Header Formatting")]
    [InfoBox("Tests section header formatting")]
    public void TestSectionHeaderFormatting()
    {
        Debug.Log("=== Section Header Formatting Tests ===");
        
        string headerFormat = TooltipFormatter.FormatSectionHeader("Stats");
        string expectedHeader = "<size=110%><b><color=#FFD700>Stats</color></b></size>";
        Debug.Log($"Header Format: {headerFormat}");
        Debug.Log($"Expected: {expectedHeader}");
        Debug.Log($"Match: {headerFormat == expectedHeader}");
    }
    
    [Button("Test Status Effect Formatting")]
    [InfoBox("Tests status effect formatting methods")]
    public void TestStatusEffectFormatting()
    {
        Debug.Log("=== Status Effect Formatting Tests ===");

        // Test ignite formatting
        string igniteFormat = TooltipFormatter.FormatStatusEffect(StatusEffectType.Ignite, 20f, 4f);
        Debug.Log($"Ignite Format:\n{igniteFormat}");

        // Test freeze formatting
        string freezeFormat = TooltipFormatter.FormatStatusEffect(StatusEffectType.Freeze, 50f, 2f);
        Debug.Log($"Freeze Format:\n{freezeFormat}");

        // Test bleed formatting
        string bleedFormat = TooltipFormatter.FormatStatusEffect(StatusEffectType.Bleed, 15f, 6f);
        Debug.Log($"Bleed Format:\n{bleedFormat}");

        // Test shock formatting
        string shockFormat = TooltipFormatter.FormatStatusEffect(StatusEffectType.Shock, 10f, 2f, "3.0m");
        Debug.Log($"Shock Format:\n{shockFormat}");

        // Test status effect modifier formatting
        string igniteModifier = TooltipFormatter.FormatStatusEffectModifier(StatusEffectType.Ignite, "Damage", 1.5f);
        Debug.Log($"Ignite Modifier: {igniteModifier}");

        string freezeModifier = TooltipFormatter.FormatStatusEffectModifier(StatusEffectType.Freeze, "Duration", 1.2f);
        Debug.Log($"Freeze Modifier: {freezeModifier}");
    }
    
    [Button("Test Gem Header Formatting")]
    [InfoBox("Tests gem header formatting")]
    public void TestGemHeaderFormatting()
    {
        Debug.Log("=== Gem Header Formatting Tests ===");
        
        // Test skill gem header
        string skillHeader = TooltipFormatter.FormatGemHeader("Fireball", GemRarity.Common, true);
        Debug.Log($"Skill Gem Header:\n{skillHeader}");
        
        // Test support gem header
        string supportHeader = TooltipFormatter.FormatGemHeader("Spell Echo", GemRarity.Rare, false);
        Debug.Log($"Support Gem Header:\n{supportHeader}");
        
        // Test gem type indicators
        string skillType = TooltipFormatter.FormatGemType(true);
        string supportType = TooltipFormatter.FormatGemType(false);
        Debug.Log($"Skill Type: {skillType}");
        Debug.Log($"Support Type: {supportType}");
    }
    
    [Button("Test Advanced Formatting Methods")]
    [InfoBox("Tests complex formatting methods")]
    public void TestAdvancedFormatting()
    {
        Debug.Log("=== Advanced Formatting Tests ===");
        
        // Test skill gem stats formatting
        string skillStats = TooltipFormatter.FormatSkillGemStats(
            damage: 25f,
            damageType: DamageType.Fire,
            cooldown: 1.5f,
            manaCost: 10f,
            attackSpeed: 1.0f,
            critChance: 5f,
            critMultiplier: 1.5f,
            supportSlots: 2
        );
        Debug.Log($"Skill Gem Stats:\n{skillStats}");
        
        // Test support gem damage modifiers
        string damageModifiers = TooltipFormatter.FormatSupportGemDamageModifiers(20f, 1.3f);
        Debug.Log($"Damage Modifiers:\n{damageModifiers}");
        
        // Test special effects formatting
        string specialEffects = TooltipFormatter.FormatSpecialEffects(
            pierce: true,
            chainCount: 2,
            extraProjectiles: 2,
            projectileSpread: 30f,
            echoCount: 1,
            echoRadius: 2f
        );
        Debug.Log($"Special Effects:\n{specialEffects}");
    }
    
    [Button("Test Color Constants")]
    [InfoBox("Validates all color constants are properly defined")]
    public void TestColorConstants()
    {
        Debug.Log("=== Color Constants Tests ===");
        
        Debug.Log($"LABEL_COLOR: {TooltipFormatter.LABEL_COLOR}");
        Debug.Log($"HEADER_COLOR: {TooltipFormatter.HEADER_COLOR}");
        Debug.Log($"VALUE_COLOR: {TooltipFormatter.VALUE_COLOR}");
        Debug.Log($"IGNITE_COLOR: {TooltipFormatter.IGNITE_COLOR}");
        Debug.Log($"FREEZE_COLOR: {TooltipFormatter.FREEZE_COLOR}");
        Debug.Log($"BLEED_COLOR: {TooltipFormatter.BLEED_COLOR}");
        Debug.Log($"SHOCK_COLOR: {TooltipFormatter.SHOCK_COLOR}");
        Debug.Log($"CORRUPTION_COLOR: {TooltipFormatter.CORRUPTION_COLOR}");
        Debug.Log($"SKILL_GEM_COLOR: {TooltipFormatter.SKILL_GEM_COLOR}");
        Debug.Log($"SUPPORT_GEM_COLOR: {TooltipFormatter.SUPPORT_GEM_COLOR}");
        
        // Test corruption status
        string corruptionStatus = TooltipFormatter.FormatCorruptionStatus();
        Debug.Log($"Corruption Status: {corruptionStatus}");
    }
    
    [Button("Test Output Consistency")]
    [InfoBox("Compares formatter output with expected original formatting")]
    public void TestOutputConsistency()
    {
        Debug.Log("=== Output Consistency Tests ===");
        
        // Test that formatter produces exact same output as original code
        
        // Original: sb.AppendFormat("\n<color=#CCCCCC>Damage:</color> <b>{0:F0}</b>", GetSkillDamage());
        string originalDamage = $"<color=#CCCCCC>Damage:</color> <b>{25:F0}</b>";
        string formatterDamage = TooltipFormatter.FormatStat("Damage", 25f, "F0");
        
        Debug.Log($"Original Damage: {originalDamage}");
        Debug.Log($"Formatter Damage: {formatterDamage}");
        Debug.Log($"Exact Match: {originalDamage == formatterDamage}");
        
        // Original: sb.AppendFormat("\n<color=#CCCCCC>• Ignite Damage:</color> <b>{0:F0}%</b>", totalIgniteDamage);
        string originalIgnite = $"<color=#CCCCCC>• Ignite Damage:</color> <b>{20:F0}%</b>";
        string formatterIgnite = TooltipFormatter.FormatStat("• Ignite Damage", 20f, "F0", "%");
        
        Debug.Log($"Original Ignite: {originalIgnite}");
        Debug.Log($"Formatter Ignite: {formatterIgnite}");
        Debug.Log($"Exact Match: {originalIgnite == formatterIgnite}");
        
        // Original: sb.Append("\n\n<size=110%><b><color=#FFD700>Stats</color></b></size>");
        string originalHeader = $"<size=110%><b><color=#FFD700>Stats</color></b></size>";
        string formatterHeader = TooltipFormatter.FormatSectionHeader("Stats");
        
        Debug.Log($"Original Header: {originalHeader}");
        Debug.Log($"Formatter Header: {formatterHeader}");
        Debug.Log($"Exact Match: {originalHeader == formatterHeader}");
    }
    
    [Button("Performance Test")]
    [InfoBox("Tests performance of formatter methods")]
    public void TestPerformance()
    {
        Debug.Log("=== Performance Tests ===");

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Test 1000 iterations of basic formatting
        for (int i = 0; i < 1000; i++)
        {
            TooltipFormatter.FormatStat("Damage", i, "F0");
            TooltipFormatter.FormatSectionHeader("Stats");
            TooltipFormatter.FormatPercentageModifier("Test", 1.5f);
        }

        stopwatch.Stop();
        Debug.Log($"1000 iterations completed in {stopwatch.ElapsedMilliseconds}ms");
        Debug.Log($"Average per call: {stopwatch.ElapsedMilliseconds / 3000f:F3}ms");
    }

    [Button("Test GemInstance Refactor")]
    [InfoBox("Validates that refactored GemInstance produces identical output")]
    public void TestGemInstanceRefactor()
    {
        Debug.Log("=== GemInstance Refactor Validation ===");

        #if UNITY_EDITOR
        // Test with a real gem asset if available
        var fireballAsset = UnityEditor.AssetDatabase.LoadAssetAtPath<SkillGemData>("Assets/Data/Gems/Skills/Fireball.asset");
        if (fireballAsset != null)
        {
            var gemInstance = new GemInstance(fireballAsset, GemRarity.Common);
            string tooltip = gemInstance.GetTooltipText();

            Debug.Log("=== REFACTORED GEMINSTANCE TOOLTIP ===");
            Debug.Log(tooltip);

            // Basic validation checks
            bool hasStatsHeader = tooltip.Contains("<size=110%><b><color=#FFD700>Stats</color></b></size>");
            bool hasLabelColor = tooltip.Contains("<color=#CCCCCC>");
            bool hasBoldValues = tooltip.Contains("<b>");

            Debug.Log($"Has Stats Header: {hasStatsHeader}");
            Debug.Log($"Has Label Color: {hasLabelColor}");
            Debug.Log($"Has Bold Values: {hasBoldValues}");
            Debug.Log($"Validation Passed: {hasStatsHeader && hasLabelColor && hasBoldValues}");
        }
        else
        {
            Debug.LogWarning("Fireball asset not found - cannot test with real gem data");
        }
        #endif
    }
}
