using System;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System.Collections.Generic;
using Sirenix.OdinInspector;

public class SkillGemSlot : MonoBehaviour, ISlot, IPointer<PERSON><PERSON><PERSON><PERSON><PERSON>, IPointer<PERSON>xit<PERSON><PERSON><PERSON>, I<PERSON><PERSON>in<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rag<PERSON><PERSON><PERSON>, IEndDragHandler, IDropHandler
{
    [Title("References")]
    [Required]
    public LayeredIconDisplay layeredIconDisplay;
    
    [Required]
    public Image borderImage;        
    [Required]
    public Transform supportSlotContainer;
    
    [Required, Obsolete("No longer needed - support slots are pre-created in the prefab")]
    public GameObject supportSlotPrefab;
    
    [Title("State")]
    [ReadOnly]
    [ShowInInspector]
    private GemInstance currentSkillGemInstance;
    
    [ReadOnly]
    [ShowInInspector]
    private List<SupportGemSlot> supportSlots = new List<SupportGemSlot>();
    
    // Pre-created support slots
    private List<SupportGemSlot> allSupportSlots = new List<SupportGemSlot>();
    
    private EquipmentPanel equipmentPanel;
    private InventoryManager inventoryManager;
    private int slotIndex;
    
    // ISlot implementation
    public bool IsEmpty => currentSkillGemInstance == null;
    
    // New instance-based properties
    public GemInstance CurrentGemInstance => currentSkillGemInstance;
    
    // Legacy support
    public GemData CurrentGem => currentSkillGemInstance?.gemDataTemplate;
    
    private void Awake()
    {
        UpdateVisuals();            
        // Try to find InventoryManager if not set
        if (inventoryManager == null)
        {
            inventoryManager = FindFirstObjectByType<InventoryManager>();
        }
        
        // Find and cache all pre-created support slots
        CachePreCreatedSupportSlots();
    }
    
    private void CachePreCreatedSupportSlots()
    {
        allSupportSlots.Clear();
        if (supportSlotContainer != null)
        {
            var slots = supportSlotContainer.GetComponentsInChildren<SupportGemSlot>(true); // Include inactive
            allSupportSlots.AddRange(slots);
            
            // Initialize all slots
            foreach (var slot in allSupportSlots)
            {
                slot.Initialize(this, inventoryManager);
            }
        }
    }
    
    public void Initialize(EquipmentPanel panel, InventoryManager inventory, int index)
    {
        equipmentPanel = panel;
        inventoryManager = inventory;
        slotIndex = index;
        
        // Fallback if not initialized properly
        if (inventoryManager == null)
        {
            inventoryManager = FindFirstObjectByType<InventoryManager>();
        }
        
        // Ensure support slots are cached
        if (allSupportSlots.Count == 0)
        {
            CachePreCreatedSupportSlots();
        }
    }
    
    public int GetSlotIndex()
    {
        return slotIndex;
    }
    
    // New instance-based methods
    public bool SetSkillGemInstance(GemInstance instance)
    {
        if (instance == null)
        {
            // Clear the slot when instance is null
            RemoveGemInstance();
            return true;
        }
        
        if (!instance.IsSkillGem)
            return false;
        
        currentSkillGemInstance = instance;
        UpdateVisuals();
        CreateSupportSlots();
        return true;
    }
    
    public void SetGemInstance(GemInstance instance)
    {
        SetSkillGemInstance(instance);
    }
    
    public GemInstance RemoveGemInstance()
    {
        var instance = currentSkillGemInstance;
        
        // Don't directly manipulate inventory here - let GemManager handle it
        // The support gems will be handled by GemManager.UnequipSkillGem which calls UnequipAllSupportGems
        
        ClearSupportSlots();
        currentSkillGemInstance = null;
        UpdateVisuals();
        
        return instance;
    }
    
    // Legacy support
    public bool SetSkillGem(GemData gem)
    {
        if (gem == null)
        {
            currentSkillGemInstance = null;
            UpdateVisuals();
            CreateSupportSlots();
            return true;
        }
        
        if (!(gem is SkillGemData))
            return false;
        
        // Create new instance if needed
        if (currentSkillGemInstance?.gemDataTemplate != gem)
        {
            currentSkillGemInstance = new GemInstance(gem);
        }
        
        UpdateVisuals();
        CreateSupportSlots();
        return true;
    }
    
    public void SetGem(GemData gem)
    {
        SetSkillGem(gem);
    }
    
    public GemData RemoveGem()
    {
        var template = currentSkillGemInstance?.gemDataTemplate;
        // The support gems will be handled by GemManager.UnequipSkillGem which calls UnequipAllSupportGems
        
        ClearSupportSlots();
        currentSkillGemInstance = null;
        UpdateVisuals();
        
        return template;
    }
    
    private void UpdateVisuals()
    {
        if (currentSkillGemInstance != null)
        {
            layeredIconDisplay.SetGem(currentSkillGemInstance);
            // TODO: Add level/quality display overlay
        }
        else
        {
            layeredIconDisplay.ClearIcon();
        }
    }        
    private void CreateSupportSlots()
    {
        ClearSupportSlots();
        
        if (currentSkillGemInstance == null || !currentSkillGemInstance.IsSkillGem)
            return;
        
        int slotCount = currentSkillGemInstance.GetSupportSlotCount();
        
        // Ensure all slots are active but set availability through alpha
        for (int i = 0; i < allSupportSlots.Count; i++)
        {
            allSupportSlots[i].gameObject.SetActive(true);
            if (i < slotCount)
            {
                allSupportSlots[i].SetAvailable(true);
                supportSlots.Add(allSupportSlots[i]);
            }
            else
            {
                allSupportSlots[i].SetAvailable(false);
            }
        }
        
        // Populate with any already equipped support gems
        RefreshSupportSlots();
    }
    
    private void ClearSupportSlots()
    {
        // Keep all support slots active but set them as unavailable and clear their gems
        foreach (var slot in allSupportSlots)
        {
            if (slot != null)
            {
                slot.SetGemInstance(null);
                slot.gameObject.SetActive(true);
                slot.SetAvailable(false);
            }
        }
        supportSlots.Clear();
    }
    
    public void RefreshSupportSlots()
    {
        if (currentSkillGemInstance == null || !currentSkillGemInstance.IsSkillGem)
            return;
            
        var gemManager = FindFirstObjectByType<GemManager>();
        if (gemManager == null) return;
        
        var equippedSupportGems = gemManager.GetEquippedSupportGems(slotIndex);
        
        // Update each support slot with the correct gem
        for (int i = 0; i < supportSlots.Count; i++)
        {
            if (i < equippedSupportGems.Count)
            {
                supportSlots[i].SetGemInstance(equippedSupportGems[i]);
            }
            else
            {
                supportSlots[i].SetGemInstance(null);
            }
        }
    }
    
    private string GetCombinedTooltip()
    {
        if (currentSkillGemInstance == null || !currentSkillGemInstance.IsSkillGem)
            return "";
        
        var skillData = currentSkillGemInstance.gemDataTemplate as SkillGemData;
        if (skillData == null)
            return currentSkillGemInstance.GetTooltipText();
        
        // Get support gems
        var supportGems = GetSupportGemInstances();
        if (supportGems.Count == 0)
        {
            // No support gems, just return normal tooltip
            return currentSkillGemInstance.GetTooltipText();
        }
        
        // Create a GemSocketController to calculate combined stats
        var controller = new GemSocketController
        {
            skillGemInstance = currentSkillGemInstance,
            supportGemInstances = supportGems
        };
        
        // Build the tooltip
        var tooltip = new System.Text.StringBuilder();
        
        // Header
        tooltip.AppendLine($"<size=120%><b><color=#{ColorUtility.ToHtmlStringRGB(currentSkillGemInstance.RarityColor)}>{currentSkillGemInstance.DisplayName}</color></b></size>");
        tooltip.AppendLine($"{currentSkillGemInstance.gemDataTemplate.rarity} Skill Gem");

        if (!string.IsNullOrEmpty(skillData.description))
        {
            tooltip.AppendLine();
            tooltip.AppendLine($"{skillData.description}");
        }

        // Add spacing and group header
        tooltip.AppendLine();
        tooltip.AppendLine(TooltipFormatter.FormatSectionHeader("Combat Stats"));
        
        // Damage
        float baseDamage = currentSkillGemInstance.GetSkillDamage();
        float finalDamage = controller.CalculateFinalDamage();
        if (Mathf.Abs(finalDamage - baseDamage) > 0.01f)
        {
            float damagePercent = ((finalDamage / baseDamage) - 1f) * 100f;
            tooltip.AppendLine($"<color={TooltipFormatter.LABEL_COLOR}>Damage:</color> {baseDamage:F0} → <b>{finalDamage:F0}</b>\t({damagePercent:+0;-0}%)");
        }
        else
        {
            tooltip.AppendLine(TooltipFormatter.FormatStat("Damage", finalDamage, "F0"));
        }
        
        // Cooldown
        float baseCooldown = currentSkillGemInstance.GetSkillCooldown();
        float finalCooldown = controller.CalculateFinalCooldown();
        if (Mathf.Abs(finalCooldown - baseCooldown) > 0.01f)
        {
            float cooldownPercent = ((finalCooldown / baseCooldown) - 1f) * 100f;
            tooltip.AppendLine($"<color={TooltipFormatter.LABEL_COLOR}>Cooldown:</color> {baseCooldown:F1}s → <b>{finalCooldown:F1}s</b>\t({cooldownPercent:+0;-0}%)");
        }
        else
        {
            tooltip.AppendLine(TooltipFormatter.FormatStat("Cooldown", finalCooldown, "F1", "s"));
        }
        
        // Mana Cost
        float baseMana = currentSkillGemInstance.GetSkillManaCost();
        float finalMana = controller.CalculateFinalManaCost();
        if (Mathf.Abs(finalMana - baseMana) > 0.01f)
        {
            float manaPercent = ((finalMana / baseMana) - 1f) * 100f;
            tooltip.AppendLine($"<color={TooltipFormatter.LABEL_COLOR}>Mana Cost:</color> {baseMana:F0} → <b>{finalMana:F0}</b>\t({manaPercent:+0;-0}%)");
        }
        else
        {
            tooltip.AppendLine(TooltipFormatter.FormatStat("Mana Cost", finalMana, "F0"));
        }

        // Attack Speed
        float baseAttackSpeed = skillData.attackSpeedMultiplier;
        float finalAttackSpeed = controller.CalculateFinalAttackSpeed();
        if (Mathf.Abs(finalAttackSpeed - baseAttackSpeed) > 0.01f)
        {
            float attackSpeedPercent = ((finalAttackSpeed / baseAttackSpeed) - 1f) * 100f;
            tooltip.AppendLine($"<color={TooltipFormatter.LABEL_COLOR}>Attack Speed:</color> {baseAttackSpeed:F2}x → <b>{finalAttackSpeed:F2}x</b>\t({attackSpeedPercent:+0;-0}%)");
        }
        else
        {
            tooltip.AppendLine(TooltipFormatter.FormatStat("Attack Speed", finalAttackSpeed, "F2", "x"));
        }
        
        // Critical
        float baseCrit = skillData.critChance;
        float finalCrit = controller.CalculateFinalCritChance();
        if (Mathf.Abs(finalCrit - baseCrit) > 0.01f)
        {
            float critChancePercent = ((finalCrit / baseCrit) - 1f) * 100f;
            tooltip.AppendLine($"<color={TooltipFormatter.LABEL_COLOR}>Critical Chance:</color> {baseCrit:F1}% → <b>{finalCrit:F1}%</b>\t({critChancePercent:+0;-0}%)");
        }
        else
        {
            tooltip.AppendLine(TooltipFormatter.FormatStat("Critical Chance", finalCrit, "F1", "%"));
        }

        float baseCritMult = skillData.critMultiplier;
        float finalCritMult = controller.CalculateFinalCritMultiplier();
        if (Mathf.Abs(finalCritMult - baseCritMult) > 0.01f)
        {
            float critMultPercent = ((finalCritMult / baseCritMult) - 1f) * 100f;
            tooltip.AppendLine($"<color={TooltipFormatter.LABEL_COLOR}>Critical Multiplier:</color> {baseCritMult:F1}x → <b>{finalCritMult:F1}x</b>\t({critMultPercent:+0;-0}%)");
        }
        else
        {
            tooltip.AppendLine(TooltipFormatter.FormatStat("Critical Multiplier", finalCritMult, "F1", "x"));
        }
        
        // Check for special effects
        int projectileCount = controller.GetTotalProjectileCount();
        bool hasSpecialEffects = projectileCount > 1 || controller.HasPierce() || controller.HasChain() || controller.HasAreaDamage();
        
        if (hasSpecialEffects)
        {
            tooltip.AppendLine();
            tooltip.AppendLine();
            tooltip.AppendLine(TooltipFormatter.FormatSectionHeader("Special Effects"));

            // Projectiles
            if (projectileCount > 1)
            {
                tooltip.AppendLine($"<color={TooltipFormatter.LABEL_COLOR}>Projectiles:</color> 1 → <b>{projectileCount}</b>");
            }
            
            // Other effects as bullet points
            var effects = new List<string>();
            if (controller.HasPierce())
                effects.Add("Pierces enemies");
            if (controller.HasChain())
                effects.Add($"Chains {controller.GetChainCount()} times");
            if (controller.HasAreaDamage())
                effects.Add($"Area damage (radius: {controller.GetAreaRadius()})");
            if (projectileCount > 1)
            {
                float spread = controller.GetProjectileSpreadAngle();
                if (spread > 0)
                    effects.Add($"Fires in a {spread:F0}° spread");
            }
            
            foreach (var effect in effects)
            {
                tooltip.AppendLine($"{effect}");
            }
        }
        
        return tooltip.ToString().TrimEnd();
    }
    
    public void OnPointerEnter(PointerEventData eventData)
    {
        if (currentSkillGemInstance != null)
        {
            // Ensure we have a valid inventory manager
            if (inventoryManager == null)
            {
                inventoryManager = FindFirstObjectByType<InventoryManager>();
            }
            
            if (inventoryManager != null)
            {
                string combinedTooltip = GetCombinedTooltip();
                inventoryManager.ShowTooltip(combinedTooltip, transform.position);
            }
        }
    }
    
    public void OnPointerExit(PointerEventData eventData)
    {
        // Ensure we have a valid inventory manager
        if (inventoryManager == null)
        {
            inventoryManager = FindFirstObjectByType<InventoryManager>();
        }
        
        if (inventoryManager != null)
        {
            inventoryManager.HideTooltip();
        }
    }
    
    // Drag & Drop implementation
    public void OnBeginDrag(PointerEventData eventData)
    {
        if (DragDropManager.Instance != null)
            DragDropManager.Instance.StartDrag(this, eventData);
    }
    
    public void OnDrag(PointerEventData eventData)
    {
        if (DragDropManager.Instance != null)
            DragDropManager.Instance.UpdateDragPosition(eventData);
    }
    
    public void OnEndDrag(PointerEventData eventData)
    {
        if (DragDropManager.Instance != null)
            DragDropManager.Instance.EndDrag(eventData);
    }
    
    public void OnDrop(PointerEventData eventData)
    {
        // Drop is handled by DragDropManager in EndDrag
    }
    
    // New instance-based method
    public List<GemInstance> GetSupportGemInstances()
    {
        var instances = new List<GemInstance>();
        foreach (var slot in supportSlots)
        {
            var instance = slot.CurrentGemInstance;
            if (instance != null)
                instances.Add(instance);
        }
        return instances;
    }
    
    // Legacy support
    public List<GemData> GetSupportGems()
    {
        var gems = new List<GemData>();
        foreach (var slot in supportSlots)
        {
            var gem = slot.CurrentGem;
            if (gem != null)
                gems.Add(gem);
        }
        return gems;
    }
}
