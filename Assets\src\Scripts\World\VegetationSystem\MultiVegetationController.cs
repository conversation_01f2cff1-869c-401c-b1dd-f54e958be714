using UnityEngine;
using Sirenix.OdinInspector;
using System.Collections.Generic;

/// <summary>
/// Controls multiple child SpriteRenderers for varied vegetation display.
/// Each vegetation prefab can show multiple different sprites simultaneously.
/// </summary>
[RequireComponent(typeof(VegetationInstance))]
public class MultiVegetationController : MonoBehaviour, ISpawnable
{
    [Title("Multi-Vegetation Controller")]
    [SerializeField, Range(1, 5)]
    [Tooltip("Maximum number of child sprites that can be active simultaneously")]
    private int maxActiveSprites = 3;
    
    [SerializeField, Range(0f, 1f)]
    [Tooltip("Probability that each additional sprite beyond the first will be activated")]
    private float additionalSpriteProbability = 0.6f;
    
    [SerializeField]
    [Tooltip("Child GameObjects containing SpriteRenderer components")]
    private Transform[] childSpriteObjects;
    
    // Cached component references for performance
    private SpriteRenderer[] childSpriteRenderers;
    private YSortingController[] childYSortingControllers;
    private VegetationInstance vegetationInstance;
    
    // Runtime state
    private int currentActiveCount = 0;
    private List<int> activeIndices = new List<int>();
    
    void Awake()
    {
        vegetationInstance = GetComponent<VegetationInstance>();
        CacheChildComponents();
    }
    
    /// <summary>
    /// Cache all child SpriteRenderer and YSortingController components to avoid GetComponent calls
    /// </summary>
    private void CacheChildComponents()
    {
        if (childSpriteObjects == null || childSpriteObjects.Length == 0)
        {
            Debug.LogError($"MultiVegetationController on {gameObject.name}: No child sprite objects assigned!");
            return;
        }

        childSpriteRenderers = new SpriteRenderer[childSpriteObjects.Length];
        childYSortingControllers = new YSortingController[childSpriteObjects.Length];

        for (int i = 0; i < childSpriteObjects.Length; i++)
        {
            if (childSpriteObjects[i] != null)
            {
                // Cache SpriteRenderer (required)
                childSpriteRenderers[i] = childSpriteObjects[i].GetComponent<SpriteRenderer>();
                if (childSpriteRenderers[i] == null)
                {
                    Debug.LogError($"MultiVegetationController on {gameObject.name}: Child object {i} ({childSpriteObjects[i].name}) has no SpriteRenderer component!");
                }

                // Cache YSortingController (optional but recommended)
                childYSortingControllers[i] = childSpriteObjects[i].GetComponent<YSortingController>();
                if (childYSortingControllers[i] == null)
                {
                    Debug.LogWarning($"MultiVegetationController on {gameObject.name}: Child object {i} ({childSpriteObjects[i].name}) has no YSortingController component!");
                }
            }
        }
    }
    
    /// <summary>
    /// Sets multiple random sprites from the provided array with random variation
    /// </summary>
    /// <param name="sprites">Array of sprites to choose from</param>
    /// <param name="color">Color to apply to all sprites</param>
    /// <param name="flipX">Whether to flip sprites horizontally</param>
    public void SetRandomSprites(Sprite[] sprites, Color color, bool flipX = false)
    {
        if (sprites == null || sprites.Length == 0 || childSpriteRenderers == null)
        {
            ClearAllSprites();
            return;
        }
        
        // Clear all sprites first
        ClearAllSprites();
        
        // Determine how many sprites to activate (at least 1, up to maxActiveSprites)
        int spritesToActivate = DetermineActiveCount();
        
        // Randomly select which child indices to use
        SelectRandomActiveIndices(spritesToActivate);
        
        // Assign random sprites to selected children
        for (int i = 0; i < activeIndices.Count; i++)
        {
            int childIndex = activeIndices[i];
            if (childIndex < childSpriteRenderers.Length && childSpriteRenderers[childIndex] != null)
            {
                // Pick a random sprite from the array
                Sprite randomSprite = sprites[Random.Range(0, sprites.Length)];
                
                // Apply sprite, color, and flip
                childSpriteRenderers[childIndex].sprite = randomSprite;
                childSpriteRenderers[childIndex].color = color;
                childSpriteRenderers[childIndex].flipX = flipX;
                childSpriteRenderers[childIndex].enabled = true;
            }
        }
        
        currentActiveCount = activeIndices.Count;
    }
    
    /// <summary>
    /// Sets the color for all active child sprites
    /// </summary>
    /// <param name="color">Color to apply</param>
    public void SetColor(Color color)
    {
        if (childSpriteRenderers == null) return;
        
        for (int i = 0; i < activeIndices.Count; i++)
        {
            int childIndex = activeIndices[i];
            if (childIndex < childSpriteRenderers.Length && childSpriteRenderers[childIndex] != null)
            {
                childSpriteRenderers[childIndex].color = color;
            }
        }
    }
    
    /// <summary>
    /// Sets the flip state for all active child sprites
    /// </summary>
    /// <param name="flipX">Whether to flip horizontally</param>
    public void SetFlip(bool flipX)
    {
        if (childSpriteRenderers == null) return;

        for (int i = 0; i < activeIndices.Count; i++)
        {
            int childIndex = activeIndices[i];
            if (childIndex < childSpriteRenderers.Length && childSpriteRenderers[childIndex] != null)
            {
                childSpriteRenderers[childIndex].flipX = flipX;
            }
        }
    }

    /// <summary>
    /// Sets the Y-sorting offset for all active child YSortingControllers
    /// </summary>
    /// <param name="yOffset">Y-offset value for sorting</param>
    public void SetYSortingOffset(float yOffset)
    {
        if (childYSortingControllers == null) return;

        for (int i = 0; i < activeIndices.Count; i++)
        {
            int childIndex = activeIndices[i];
            if (childIndex < childYSortingControllers.Length && childYSortingControllers[childIndex] != null)
            {
                childYSortingControllers[childIndex].SetYOffset(yOffset);
            }
        }
    }
    
    /// <summary>
    /// Clears all child sprites and disables them
    /// </summary>
    public void ClearAllSprites()
    {
        if (childSpriteRenderers == null) return;
        
        for (int i = 0; i < childSpriteRenderers.Length; i++)
        {
            if (childSpriteRenderers[i] != null)
            {
                childSpriteRenderers[i].sprite = null;
                childSpriteRenderers[i].color = Color.white;
                childSpriteRenderers[i].flipX = false;
                childSpriteRenderers[i].enabled = false;
            }
        }
        
        activeIndices.Clear();
        currentActiveCount = 0;
    }
    
    /// <summary>
    /// Determines how many sprites should be active based on probability
    /// </summary>
    private int DetermineActiveCount()
    {
        int count = 1; // Always activate at least one sprite
        int maxPossible = Mathf.Min(maxActiveSprites, childSpriteRenderers.Length);
        
        // Roll for additional sprites
        for (int i = 1; i < maxPossible; i++)
        {
            if (Random.value < additionalSpriteProbability)
            {
                count++;
            }
            else
            {
                break; // Stop rolling once we fail a probability check
            }
        }
        
        return count;
    }
    
    /// <summary>
    /// Randomly selects which child indices to activate
    /// </summary>
    private void SelectRandomActiveIndices(int count)
    {
        activeIndices.Clear();
        
        // Create a list of available indices
        List<int> availableIndices = new List<int>();
        for (int i = 0; i < childSpriteRenderers.Length; i++)
        {
            if (childSpriteRenderers[i] != null)
            {
                availableIndices.Add(i);
            }
        }
        
        // Randomly select indices without replacement
        for (int i = 0; i < count && availableIndices.Count > 0; i++)
        {
            int randomIndex = Random.Range(0, availableIndices.Count);
            activeIndices.Add(availableIndices[randomIndex]);
            availableIndices.RemoveAt(randomIndex);
        }
    }
    
    /// <summary>
    /// Gets the number of currently active sprites
    /// </summary>
    public int GetActiveCount()
    {
        return currentActiveCount;
    }
    
    /// <summary>
    /// Gets the total number of available child sprites
    /// </summary>
    public int GetTotalChildCount()
    {
        return childSpriteRenderers?.Length ?? 0;
    }
    
    // ISpawnable implementation for object pooling
    public void OnSpawn()
    {
        ClearAllSprites();
    }
    
    public void OnDespawn()
    {
        ClearAllSprites();
    }
    
    #if UNITY_EDITOR
    [Button("Validate Child Setup", ButtonSizes.Medium)]
    private void ValidateChildSetup()
    {
        CacheChildComponents();

        Debug.Log($"MultiVegetationController Validation for {gameObject.name}:");
        Debug.Log($"- Child objects assigned: {childSpriteObjects?.Length ?? 0}");
        Debug.Log($"- Valid SpriteRenderers found: {childSpriteRenderers?.Length ?? 0}");
        Debug.Log($"- Valid YSortingControllers found: {childYSortingControllers?.Length ?? 0}");
        Debug.Log($"- Max active sprites: {maxActiveSprites}");
        Debug.Log($"- Additional sprite probability: {additionalSpriteProbability:P0}");

        if (childSpriteRenderers != null && childYSortingControllers != null)
        {
            for (int i = 0; i < childSpriteRenderers.Length; i++)
            {
                string spriteStatus = childSpriteRenderers[i] != null ? "✓ SpriteRenderer" : "✗ Missing SpriteRenderer";
                string ySortStatus = childYSortingControllers[i] != null ? "✓ YSortingController" : "✗ Missing YSortingController";
                Debug.Log($"  Child {i}: {spriteStatus}, {ySortStatus}");
            }
        }
    }
    
    [Button("Test Random Sprites", ButtonSizes.Medium)]
    private void TestRandomSprites()
    {
        if (!Application.isPlaying)
        {
            Debug.LogWarning("Can only test in play mode!");
            return;
        }
        
        // Create some dummy sprites for testing
        Sprite[] testSprites = new Sprite[3];
        // In a real scenario, these would come from BiomeData
        
        SetRandomSprites(testSprites, Color.green, Random.value > 0.5f);
        Debug.Log($"Test: Activated {GetActiveCount()} out of {GetTotalChildCount()} child sprites");
    }
    #endif
}
