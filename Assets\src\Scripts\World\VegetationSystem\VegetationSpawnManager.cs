using UnityEngine;
using System.Collections.Generic;
using Sirenix.OdinInspector;

[RequireComponent(typeof(TilemapChunkManager))]
public class VegetationSpawnManager : MonoBehaviour
{
    [Title("Vegetation Spawn Manager")]
    [SerializeF<PERSON>, Required]
    [Tooltip("Prefab used for low grass vegetation (shorter, smaller plants)")]
    private GameObject lowGrassVegetationPrefab;

    [SerializeField, Required]
    [Tooltip("Prefab used for high grass vegetation (taller, larger plants)")]
    private GameObject highGrassVegetationPrefab;
    
    [SerializeField]
    [Tooltip("Parent transform for all spawned vegetation")]
    private Transform vegetationParent;
    
    [SerializeField]
    [Tooltip("Initial pool size for vegetation objects")]
    private int initialPoolSize = 100;
    
    [SerializeField, Range(0f, 1f)]
    [Tooltip("Minimum spacing between vegetation objects (in units)")]
    private float minimumSpacing = 0.3f;
    
    [SerializeField]
    [Tooltip("Maximum vegetation objects per chunk")]
    private int maxVegetationPerChunk = 50;
    
    [SerializeField]
    [Tooltip("Enable debug logging")]
    private bool debugMode = false;
    
    // Singleton instance
    public static VegetationSpawnManager Instance { get; private set; }
    
    // References
    private TilemapChunkManager chunkManager;
    private BiomeManager biomeManager;
    
    // Tracking spawned vegetation per chunk
    private Dictionary<ChunkCoordinate, List<GameObject>> chunkVegetation = new Dictionary<ChunkCoordinate, List<GameObject>>();
    
    // Pool configuration tracking
    private bool poolConfigured = false;
    
    // Cached collections to avoid allocations
    private List<ChunkCoordinate> chunksToRemove = new List<ChunkCoordinate>();
    private List<VegetationPlacement> placementCache = new List<VegetationPlacement>(256);
    
    // Struct for vegetation placement data
    private struct VegetationPlacement
    {
        public Vector3 position;
        public Sprite sprite;
        public float scale;
        public float rotation;
        public bool flipX;
        public Color color;
        public bool isHighGrass; // true = high grass prefab, false = low grass prefab
    }
    
    void Awake()
    {
        if (Instance == null)
            Instance = this;
        else if (Instance != this)
            Destroy(gameObject);
            
        chunkManager = GetComponent<TilemapChunkManager>();
        
        if (vegetationParent == null)
        {
            GameObject parentObj = new GameObject("Vegetation");
            parentObj.transform.SetParent(transform);
            vegetationParent = parentObj.transform;
        }
        
        // Validate vegetation prefabs have required components
        ValidateVegetationPrefab(lowGrassVegetationPrefab, "Low Grass");
        ValidateVegetationPrefab(highGrassVegetationPrefab, "High Grass");
    }

    private void ValidateVegetationPrefab(GameObject prefab, string prefabType)
    {
        if (prefab != null)
        {
            if (prefab.GetComponent<VegetationInstance>() == null)
            {
                Debug.LogError($"VegetationSpawnManager: {prefabType} vegetation prefab must have VegetationInstance component!");
            }
            if (prefab.GetComponent<YSortingController>() == null)
            {
                Debug.LogWarning($"VegetationSpawnManager: {prefabType} vegetation prefab should have YSortingController component!");
            }
        }
        else
        {
            Debug.LogError($"VegetationSpawnManager: {prefabType} vegetation prefab is not assigned!");
        }
    }
    
    void Start()
    {
        // Get biome manager reference
        biomeManager = chunkManager.GetBiomeManager();
            
        if (biomeManager == null)
        {
            Debug.LogError("VegetationSpawnManager: Could not access BiomeManager from TilemapChunkManager!");
        }
        
        // Configure pool on start
        ConfigurePool();
    }
    
    public void OnChunkGenerated(ChunkCoordinate coord, int seed)
    {
        if (lowGrassVegetationPrefab == null || highGrassVegetationPrefab == null)
        {
            if (debugMode)
                Debug.LogWarning("VegetationSpawnManager: Low grass or high grass vegetation prefab not assigned!");
            return;
        }

        if (debugMode)
            Debug.Log($"Generating vegetation for chunk {coord} with seed {seed}");

        GenerateVegetationForChunk(coord, seed);
    }
    
    public void OnChunkUnloaded(ChunkCoordinate coord)
    {
        if (debugMode)
            Debug.Log($"Unloading vegetation for chunk {coord}");
            
        DespawnVegetationForChunk(coord);
    }
    
    private void GenerateVegetationForChunk(ChunkCoordinate coord, int baseSeed)
    {
        // Use deterministic seed based on chunk coordinates and base seed
        int chunkSeed = baseSeed + coord.GetHashCode() + 3000; // Different offset than tiles
        Random.State originalState = Random.state;
        Random.InitState(chunkSeed);
        
        // Calculate chunk bounds
        int chunkWidth = chunkManager.GetChunkWidth();
        int chunkHeight = chunkManager.GetChunkHeight();
        Vector3 chunkWorldPos = coord.ToWorldPosition(chunkWidth, chunkHeight);
        
        // Clear placement cache
        placementCache.Clear();
        
        // Generate placement data first (deterministic)
        for (int x = 0; x < chunkWidth; x++)
        {
            for (int y = 0; y < chunkHeight; y++)
            {
                Vector3Int tilePos = new Vector3Int(
                    Mathf.FloorToInt(chunkWorldPos.x) + x,
                    Mathf.FloorToInt(chunkWorldPos.y) + y,
                    0
                );
                
                // Get biome for this position
                BiomeData biome = biomeManager.GetBiomeForWorldPosition(tilePos, chunkManager.GetChunkSize());
                
                if (biome != null && biome.HasVegetation())
                {
                    // Check if we should place vegetation here
                    if (ShouldPlaceVegetation(biome, tilePos))
                    {
                        // Determine if this should be high grass or low grass
                        bool useHighGrass = Random.value < biome.HighGrassRatio;

                        // Get appropriate sprite based on grass type
                        Sprite vegetationSprite = useHighGrass
                            ? biome.GetRandomHighGrassVegetationSprite()
                            : biome.GetRandomLowGrassVegetationSprite();

                        if (vegetationSprite != null)
                        {
                            // Calculate position with slight randomization
                            Vector3 position = new Vector3(tilePos.x + 0.5f, tilePos.y + 0.5f, 0);
                            position.x += Random.Range(-0.4f, 0.4f);
                            position.y += Random.Range(-0.4f, 0.4f);

                            // Check minimum spacing
                            if (CheckMinimumSpacing(position, minimumSpacing))
                            {
                                VegetationPlacement placement = new VegetationPlacement
                                {
                                    position = position,
                                    sprite = vegetationSprite,
                                    scale = Random.Range(biome.VegetationMinScale, biome.VegetationMaxScale),
                                    rotation = Random.Range(-biome.VegetationMaxRotation, biome.VegetationMaxRotation),
                                    flipX = Random.value < biome.VegetationFlipChance,
                                    color = biome.VegetationColor,
                                    isHighGrass = useHighGrass
                                };
                                
                                placementCache.Add(placement);
                                
                                // Check max per chunk limit
                                if (placementCache.Count >= maxVegetationPerChunk)
                                    break;
                            }
                        }
                    }
                }
            }
            
            if (placementCache.Count >= maxVegetationPerChunk)
                break;
        }
        
        // Restore random state
        Random.state = originalState;
        
        // Now spawn the vegetation using the pool manager
        SpawnVegetationFromPlacements(coord);
    }
    
    private bool ShouldPlaceVegetation(BiomeData biome, Vector3Int tilePos)
    {
        float vegetationChance = biome.VegetationDensity * biome.VegetationDensityMultiplier;
        
        // Apply noise-based clustering if enabled
        if (biome.UseVegetationClustering)
        {
            float noiseValue = Mathf.PerlinNoise(
                (tilePos.x * biome.VegetationNoiseScale) + biome.VegetationNoiseOffset.x,
                (tilePos.y * biome.VegetationNoiseScale) + biome.VegetationNoiseOffset.y
            );
            
            // Only place vegetation if noise is above threshold
            if (noiseValue < biome.VegetationNoiseThreshold)
            {
                return false;
            }
            
            // Modulate vegetation chance based on noise
            vegetationChance *= (noiseValue - biome.VegetationNoiseThreshold) / (1f - biome.VegetationNoiseThreshold);
        }
        
        return Random.value < vegetationChance;
    }
    
    private bool CheckMinimumSpacing(Vector3 position, float minimumSpacing)
    {
        if (minimumSpacing <= 0) return true;
        
        float spacingSqr = minimumSpacing * minimumSpacing;
        
        foreach (var placement in placementCache)
        {
            float distSqr = (placement.position - position).sqrMagnitude;
            if (distSqr < spacingSqr)
                return false;
        }
        
        return true;
    }
    
    private void SpawnVegetationFromPlacements(ChunkCoordinate coord)
    {
        if (placementCache.Count == 0) return;
        
        // Ensure we have a list for this chunk
        if (!chunkVegetation.ContainsKey(coord))
        {
            chunkVegetation[coord] = new List<GameObject>(placementCache.Count);
        }
        
        List<GameObject> vegetationList = chunkVegetation[coord];
        
        foreach (var placement in placementCache)
        {
            // Choose the correct prefab based on grass type
            GameObject prefabToUse = placement.isHighGrass ? highGrassVegetationPrefab : lowGrassVegetationPrefab;

            // Spawn from pool
            GameObject vegetation = PoolManager.Instance.Spawn(
                prefabToUse,
                placement.position,
                Quaternion.Euler(0, 0, placement.rotation),
                vegetationParent
            );
            
            if (vegetation != null)
            {
                // Cache components to avoid multiple GetComponent calls
                VegetationInstance instance = vegetation.GetComponent<VegetationInstance>();
                SpriteRenderer spriteRenderer = vegetation.GetComponent<SpriteRenderer>();
                YSortingController ySorting = vegetation.GetComponent<YSortingController>();

                // Set sprite and color using VegetationInstance
                if (instance != null)
                {
                    instance.SetSprite(placement.sprite);
                    instance.SetColor(placement.color);
                }

                // Apply scale
                vegetation.transform.localScale = Vector3.one * placement.scale;

                // Apply flip using cached SpriteRenderer
                if (placement.flipX && spriteRenderer != null)
                {
                    spriteRenderer.flipX = true;
                }

                // Set Y-sorting offset using cached YSortingController
                if (ySorting != null)
                {
                    // Get biome for this position to get Y-sorting offset
                    BiomeData biome = biomeManager.GetBiomeForWorldPosition(
                        new Vector3Int(Mathf.RoundToInt(placement.position.x), Mathf.RoundToInt(placement.position.y), 0),
                        chunkManager.GetChunkSize()
                    );

                    if (biome != null)
                    {
                        ySorting.SetYOffset(biome.VegetationYSortingOffset);
                    }
                }

                // Track this vegetation object
                vegetationList.Add(vegetation);
            }
        }
        
        if (debugMode)
            Debug.Log($"Spawned {vegetationList.Count} vegetation objects in chunk {coord}");
    }
    
    private void ConfigurePool()
    {
        if (poolConfigured || lowGrassVegetationPrefab == null || highGrassVegetationPrefab == null) return;

        // Pre-warm pool by spawning and immediately despawning objects for both prefab types
        List<GameObject> tempObjects = new List<GameObject>(initialPoolSize * 2);

        // Pre-warm low grass vegetation pool
        for (int i = 0; i < initialPoolSize / 2; i++)
        {
            GameObject obj = PoolManager.Instance.Spawn(
                lowGrassVegetationPrefab,
                Vector3.zero,
                Quaternion.identity,
                vegetationParent
            );

            if (obj != null)
                tempObjects.Add(obj);
        }

        // Pre-warm high grass vegetation pool
        for (int i = 0; i < initialPoolSize / 2; i++)
        {
            GameObject obj = PoolManager.Instance.Spawn(
                highGrassVegetationPrefab,
                Vector3.zero,
                Quaternion.identity,
                vegetationParent
            );

            if (obj != null)
                tempObjects.Add(obj);
        }
        
        // Immediately return them to the pool
        foreach (var obj in tempObjects)
        {
            PoolManager.Instance.Despawn(obj);
        }
        
        poolConfigured = true;
        
        if (debugMode)
            Debug.Log($"Pre-warmed vegetation pool with {tempObjects.Count} objects ({initialPoolSize / 2} low grass, {initialPoolSize / 2} high grass)");
    }
    
    private void DespawnVegetationForChunk(ChunkCoordinate coord)
    {
        if (!chunkVegetation.ContainsKey(coord))
            return;
            
        List<GameObject> vegetationList = chunkVegetation[coord];
        
        foreach (var vegetation in vegetationList)
        {
            if (vegetation != null)
            {
                PoolManager.Instance.Despawn(vegetation);
            }
        }
        
        vegetationList.Clear();
        chunkVegetation.Remove(coord);
    }
    
    public void ClearAllVegetation()
    {
        chunksToRemove.Clear();
        chunksToRemove.AddRange(chunkVegetation.Keys);
        
        foreach (var coord in chunksToRemove)
        {
            DespawnVegetationForChunk(coord);
        }
        
        chunkVegetation.Clear();
    }
    
    #if UNITY_EDITOR
    [Button("Debug: Spawn Test Vegetation", ButtonSizes.Large)]
    void DebugSpawnTestVegetation()
    {
        if (!Application.isPlaying)
        {
            Debug.LogWarning("Can only test in play mode!");
            return;
        }
        
        ChunkCoordinate currentChunk = chunkManager.GetCurrentPlayerChunk();
        OnChunkGenerated(currentChunk, Random.Range(0, 10000));
    }
    
    [Button("Debug: Clear All Vegetation", ButtonSizes.Large)]
    void DebugClearAllVegetation()
    {
        if (!Application.isPlaying)
        {
            Debug.LogWarning("Can only test in play mode!");
            return;
        }
        
        ClearAllVegetation();
        Debug.Log("Cleared all vegetation");
    }
    
    [Button("Debug: Log Vegetation Stats", ButtonSizes.Medium)]
    public void DebugLogStats()
    {
        Debug.Log("=== Vegetation Stats ===");
        Debug.Log($"Active Chunks: {chunkVegetation.Count}");
        
        int totalVegetation = 0;
        foreach (var kvp in chunkVegetation)
        {
            int count = kvp.Value.Count;
            totalVegetation += count;
            Debug.Log($"  Chunk {kvp.Key}: {count} vegetation objects");
        }
        
        Debug.Log($"Total Vegetation Objects: {totalVegetation}");
        Debug.Log($"Pool Configured: {poolConfigured}");
        Debug.Log("========================");
    }
    #endif
}